<resources>
    <string name="app_name">Anime Plus</string>

    <!-- Navigation -->
    <string name="nav_home">Home</string>
    <string name="nav_search">Search</string>
    <string name="nav_favorites">Favorites</string>
    <string name="nav_profile">Profile</string>

    <!-- Home Screen -->
    <string name="home_trending">🔥 Trending Now</string>
    <string name="home_trending_subtitle">Most popular anime</string>
    <string name="home_continue_watching">📺 Continue Watching</string>
    <string name="home_continue_watching_subtitle">Pick up where you left off</string>
    <string name="home_current_season">🌸 This Season</string>
    <string name="home_current_season_subtitle">Currently airing anime</string>
    <string name="home_popular">⭐ Popular Now</string>
    <string name="home_popular_subtitle">Most watched this week</string>
    <string name="home_recent_episodes">🆕 Latest Episodes</string>
    <string name="home_recent_episodes_subtitle">Fresh episodes just released</string>
    <string name="home_upcoming">🔮 Coming Soon</string>
    <string name="home_upcoming_subtitle">Upcoming anime releases</string>

    <!-- Search Screen -->
    <string name="search_hint">Search anime...</string>
    <string name="search_no_results">No Results Found</string>
    <string name="search_no_results_subtitle">Try searching with different keywords or adjust your filters.</string>
    <string name="search_empty_title">Search for Anime</string>
    <string name="search_empty_subtitle">Find your favorite anime by title, genre, or year</string>
    <string name="search_popular_searches">Popular Searches:</string>

    <!-- Common -->
    <string name="loading">Loading...</string>
    <string name="error_occurred">An error occurred</string>
    <string name="retry">Retry</string>
    <string name="try_again">Try Again</string>
    <string name="cancel">Cancel</string>
    <string name="ok">OK</string>
    <string name="back">Back</string>
    <string name="share">Share</string>
    <string name="refresh">Refresh</string>
    <string name="see_all">See All</string>
    <string name="clear">Clear</string>
    <string name="sort">Sort</string>
    <string name="filter">Filter</string>
    <string name="search">Search</string>
    <string name="play">Play</string>
    <string name="pause">Pause</string>
    <string name="stop">Stop</string>
</resources>