package com.animeplus.app.presentation.screens.favorites

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Favorite
import androidx.compose.material.icons.filled.Sort
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.animeplus.app.presentation.components.*
import com.animeplus.app.presentation.viewmodel.FavoritesViewModel

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun FavoritesScreen(
    onAnimeClick: (Int) -> Unit,
    viewModel: FavoritesViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsState()
    var showSortMenu by remember { mutableStateOf(false) }
    
    LaunchedEffect(Unit) {
        viewModel.loadFavorites()
    }
    
    Column(
        modifier = Modifier.fillMaxSize()
    ) {
        // Top App Bar
        TopAppBar(
            title = {
                Text(
                    text = "My Favorites",
                    style = MaterialTheme.typography.headlineMedium,
                    fontWeight = FontWeight.Bold
                )
            },
            actions = {
                if (uiState.favorites.isNotEmpty()) {
                    IconButton(onClick = { showSortMenu = true }) {
                        Icon(
                            imageVector = Icons.Default.Sort,
                            contentDescription = "Sort"
                        )
                    }
                    
                    DropdownMenu(
                        expanded = showSortMenu,
                        onDismissRequest = { showSortMenu = false }
                    ) {
                        DropdownMenuItem(
                            text = { Text("Recently Added") },
                            onClick = {
                                viewModel.sortFavorites(FavoritesSortType.RECENTLY_ADDED)
                                showSortMenu = false
                            }
                        )
                        DropdownMenuItem(
                            text = { Text("Title A-Z") },
                            onClick = {
                                viewModel.sortFavorites(FavoritesSortType.TITLE_ASC)
                                showSortMenu = false
                            }
                        )
                        DropdownMenuItem(
                            text = { Text("Title Z-A") },
                            onClick = {
                                viewModel.sortFavorites(FavoritesSortType.TITLE_DESC)
                                showSortMenu = false
                            }
                        )
                        DropdownMenuItem(
                            text = { Text("Highest Score") },
                            onClick = {
                                viewModel.sortFavorites(FavoritesSortType.SCORE_DESC)
                                showSortMenu = false
                            }
                        )
                    }
                }
            }
        )
        
        // Content
        when {
            uiState.isLoading -> {
                FullScreenLoading("Loading favorites...")
            }
            
            uiState.error != null -> {
                FullScreenError(
                    title = "Failed to Load Favorites",
                    message = uiState.error,
                    onRetry = { viewModel.loadFavorites() }
                )
            }
            
            uiState.favorites.isEmpty() -> {
                EmptyFavoritesState()
            }
            
            else -> {
                LazyVerticalGrid(
                    columns = GridCells.Fixed(2),
                    contentPadding = PaddingValues(16.dp),
                    horizontalArrangement = Arrangement.spacedBy(12.dp),
                    verticalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    items(uiState.favorites) { favorite ->
                        FavoriteAnimeCard(
                            favorite = favorite,
                            onClick = { onAnimeClick(favorite.animeId) },
                            onRemoveClick = { viewModel.removeFromFavorites(favorite.animeId) }
                        )
                    }
                }
            }
        }
    }
}

@Composable
fun EmptyFavoritesState() {
    EmptyState(
        title = "No Favorites Yet",
        message = "Start adding anime to your favorites to see them here. Tap the heart icon on any anime to add it to your collection.",
        icon = Icons.Default.Favorite
    )
}

// Placeholder ViewModel and related classes
@androidx.lifecycle.ViewModel
class FavoritesViewModel @javax.inject.Inject constructor() : androidx.lifecycle.ViewModel() {
    
    private val _uiState = kotlinx.coroutines.flow.MutableStateFlow(FavoritesUiState())
    val uiState: kotlinx.coroutines.flow.StateFlow<FavoritesUiState> = _uiState.asStateFlow()
    
    fun loadFavorites() {
        // Implementation
    }
    
    fun sortFavorites(sortType: FavoritesSortType) {
        // Implementation
    }
    
    fun removeFromFavorites(animeId: Int) {
        // Implementation
    }
}

data class FavoritesUiState(
    val isLoading: Boolean = false,
    val favorites: List<com.animeplus.app.data.model.FavoriteAnime> = emptyList(),
    val error: String? = null
)

enum class FavoritesSortType {
    RECENTLY_ADDED,
    TITLE_ASC,
    TITLE_DESC,
    SCORE_DESC
}

@Composable
fun FavoriteAnimeCard(
    favorite: com.animeplus.app.data.model.FavoriteAnime,
    onClick: () -> Unit,
    onRemoveClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        onClick = onClick,
        modifier = modifier,
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Box {
            Column {
                // Anime poster
                AsyncImage(
                    model = favorite.imageUrl,
                    contentDescription = favorite.title,
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(200.dp),
                    contentScale = androidx.compose.ui.layout.ContentScale.Crop
                )
                
                // Info section
                Column(
                    modifier = Modifier.padding(12.dp)
                ) {
                    Text(
                        text = favorite.title,
                        style = MaterialTheme.typography.titleSmall,
                        fontWeight = FontWeight.Medium,
                        maxLines = 2,
                        overflow = androidx.compose.ui.text.style.TextOverflow.Ellipsis
                    )
                    
                    Spacer(modifier = Modifier.height(4.dp))
                    
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        favorite.score?.let { score ->
                            ScoreBadge(score = score)
                        }
                        
                        favorite.episodes?.let { episodes ->
                            Text(
                                text = "$episodes EP",
                                style = MaterialTheme.typography.bodySmall,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                    }
                }
            }
            
            // Remove button
            IconButton(
                onClick = onRemoveClick,
                modifier = Modifier
                    .align(Alignment.TopEnd)
                    .padding(8.dp)
            ) {
                Icon(
                    imageVector = Icons.Default.Favorite,
                    contentDescription = "Remove from favorites",
                    tint = MaterialTheme.colorScheme.primary
                )
            }
        }
    }
}
