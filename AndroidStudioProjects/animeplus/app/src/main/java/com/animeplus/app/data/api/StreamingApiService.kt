package com.animeplus.app.data.api

import com.animeplus.app.data.model.*
import retrofit2.Response
import retrofit2.http.GET
import retrofit2.http.Path
import retrofit2.http.Query

interface StreamingApiService {
    
    @GET("search")
    suspend fun searchAnime(
        @Query("keyw") keyword: String,
        @Query("page") page: Int = 1
    ): Response<StreamingSearchResponse>
    
    @GET("info/{id}")
    suspend fun getAnimeInfo(
        @Path("id") animeId: String
    ): Response<StreamingAnime>
    
    @GET("watch/{episodeId}")
    suspend fun getEpisodeStreamingData(
        @Path("episodeId") episodeId: String
    ): Response<StreamingData>
    
    @GET("recent-episodes")
    suspend fun getRecentEpisodes(
        @Query("page") page: Int = 1,
        @Query("type") type: Int = 1 // 1 for sub, 2 for dub, 3 for chinese
    ): Response<RecentEpisodesResponse>
    
    @GET("popular")
    suspend fun getPopularAnime(
        @Query("page") page: Int = 1
    ): Response<StreamingSearchResponse>
    
    @GET("anime-movies")
    suspend fun getAnimeMovies(
        @Query("page") page: Int = 1
    ): Response<StreamingSearchResponse>
    
    @GET("top-airing")
    suspend fun getTopAiring(
        @Query("page") page: Int = 1
    ): Response<StreamingSearchResponse>
}

@kotlinx.serialization.Serializable
data class RecentEpisode(
    @kotlinx.serialization.SerialName("id")
    val id: String,
    
    @kotlinx.serialization.SerialName("episodeId")
    val episodeId: String,
    
    @kotlinx.serialization.SerialName("episodeNumber")
    val episodeNumber: Int,
    
    @kotlinx.serialization.SerialName("title")
    val title: String,
    
    @kotlinx.serialization.SerialName("image")
    val image: String? = null,
    
    @kotlinx.serialization.SerialName("url")
    val url: String
)

@kotlinx.serialization.Serializable
data class RecentEpisodesResponse(
    @kotlinx.serialization.SerialName("results")
    val results: List<RecentEpisode> = emptyList(),
    
    @kotlinx.serialization.SerialName("hasNextPage")
    val hasNextPage: Boolean = false,
    
    @kotlinx.serialization.SerialName("currentPage")
    val currentPage: Int = 1
)

// Alternative streaming sources
interface AlternativeStreamingService {
    
    @GET("anime/{slug}")
    suspend fun getAnimeBySlug(
        @Path("slug") slug: String
    ): Response<AlternativeAnime>
    
    @GET("anime/{slug}/episodes")
    suspend fun getAnimeEpisodes(
        @Path("slug") slug: String,
        @Query("page") page: Int = 1
    ): Response<AlternativeEpisodesResponse>
    
    @GET("episode/{episodeId}/sources")
    suspend fun getEpisodeSources(
        @Path("episodeId") episodeId: String
    ): Response<AlternativeSourcesResponse>
}

@kotlinx.serialization.Serializable
data class AlternativeAnime(
    @kotlinx.serialization.SerialName("id")
    val id: String,
    
    @kotlinx.serialization.SerialName("title")
    val title: String,
    
    @kotlinx.serialization.SerialName("slug")
    val slug: String,
    
    @kotlinx.serialization.SerialName("description")
    val description: String? = null,
    
    @kotlinx.serialization.SerialName("image")
    val image: String? = null,
    
    @kotlinx.serialization.SerialName("cover")
    val cover: String? = null,
    
    @kotlinx.serialization.SerialName("totalEpisodes")
    val totalEpisodes: Int? = null,
    
    @kotlinx.serialization.SerialName("status")
    val status: String? = null,
    
    @kotlinx.serialization.SerialName("releaseDate")
    val releaseDate: String? = null,
    
    @kotlinx.serialization.SerialName("genres")
    val genres: List<String> = emptyList()
)

@kotlinx.serialization.Serializable
data class AlternativeEpisode(
    @kotlinx.serialization.SerialName("id")
    val id: String,
    
    @kotlinx.serialization.SerialName("title")
    val title: String? = null,
    
    @kotlinx.serialization.SerialName("number")
    val number: Int,
    
    @kotlinx.serialization.SerialName("url")
    val url: String
)

@kotlinx.serialization.Serializable
data class AlternativeEpisodesResponse(
    @kotlinx.serialization.SerialName("episodes")
    val episodes: List<AlternativeEpisode> = emptyList(),
    
    @kotlinx.serialization.SerialName("totalEpisodes")
    val totalEpisodes: Int = 0
)

@kotlinx.serialization.Serializable
data class AlternativeSource(
    @kotlinx.serialization.SerialName("url")
    val url: String,
    
    @kotlinx.serialization.SerialName("quality")
    val quality: String? = null,
    
    @kotlinx.serialization.SerialName("isM3U8")
    val isM3U8: Boolean = false
)

@kotlinx.serialization.Serializable
data class AlternativeSourcesResponse(
    @kotlinx.serialization.SerialName("sources")
    val sources: List<AlternativeSource> = emptyList(),
    
    @kotlinx.serialization.SerialName("subtitles")
    val subtitles: List<SubtitleTrack> = emptyList()
)

@kotlinx.serialization.Serializable
data class SubtitleTrack(
    @kotlinx.serialization.SerialName("url")
    val url: String,
    
    @kotlinx.serialization.SerialName("lang")
    val language: String
)
