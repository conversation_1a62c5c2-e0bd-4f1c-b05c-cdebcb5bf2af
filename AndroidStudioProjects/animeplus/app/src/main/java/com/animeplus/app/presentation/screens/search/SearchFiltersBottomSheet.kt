package com.animeplus.app.presentation.screens.search

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.selection.selectable
import androidx.compose.foundation.selection.selectableGroup
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.semantics.Role
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.animeplus.app.presentation.viewmodel.SearchFilters

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SearchFiltersBottomSheet(
    filters: SearchFilters,
    onFiltersChanged: (SearchFilters) -> Unit,
    onDismiss: () -> Unit
) {
    var currentFilters by remember { mutableStateOf(filters) }
    
    ModalBottomSheet(
        onDismissRequest = onDismiss,
        modifier = Modifier.fillMaxHeight(0.8f)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            // Header
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "Filters",
                    style = MaterialTheme.typography.headlineSmall,
                    fontWeight = FontWeight.Bold
                )
                
                Row {
                    TextButton(
                        onClick = {
                            currentFilters = SearchFilters()
                        }
                    ) {
                        Text("Clear All")
                    }
                    
                    Button(
                        onClick = {
                            onFiltersChanged(currentFilters)
                            onDismiss()
                        }
                    ) {
                        Text("Apply")
                    }
                }
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // Sort By Section
            FilterSection(
                title = "Sort By"
            ) {
                val sortOptions = listOf(
                    "score" to "Score",
                    "popularity" to "Popularity",
                    "title" to "Title",
                    "start_date" to "Release Date"
                )
                
                Column(modifier = Modifier.selectableGroup()) {
                    sortOptions.forEach { (value, label) ->
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .selectable(
                                    selected = currentFilters.sortBy == value,
                                    onClick = {
                                        currentFilters = currentFilters.copy(sortBy = value)
                                    },
                                    role = Role.RadioButton
                                )
                                .padding(vertical = 8.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            RadioButton(
                                selected = currentFilters.sortBy == value,
                                onClick = null
                            )
                            Spacer(modifier = Modifier.width(8.dp))
                            Text(text = label)
                        }
                    }
                }
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // Sort Order Section
            FilterSection(
                title = "Order"
            ) {
                Row(modifier = Modifier.selectableGroup()) {
                    Row(
                        modifier = Modifier
                            .selectable(
                                selected = currentFilters.sortOrder == "desc",
                                onClick = {
                                    currentFilters = currentFilters.copy(sortOrder = "desc")
                                },
                                role = Role.RadioButton
                            )
                            .padding(end = 16.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        RadioButton(
                            selected = currentFilters.sortOrder == "desc",
                            onClick = null
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text("Descending")
                    }
                    
                    Row(
                        modifier = Modifier
                            .selectable(
                                selected = currentFilters.sortOrder == "asc",
                                onClick = {
                                    currentFilters = currentFilters.copy(sortOrder = "asc")
                                },
                                role = Role.RadioButton
                            ),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        RadioButton(
                            selected = currentFilters.sortOrder == "asc",
                            onClick = null
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text("Ascending")
                    }
                }
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // Status Section
            FilterSection(
                title = "Status"
            ) {
                val statusOptions = listOf(
                    null to "All",
                    "airing" to "Currently Airing",
                    "complete" to "Completed",
                    "upcoming" to "Upcoming"
                )
                
                Column(modifier = Modifier.selectableGroup()) {
                    statusOptions.forEach { (value, label) ->
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .selectable(
                                    selected = currentFilters.status == value,
                                    onClick = {
                                        currentFilters = currentFilters.copy(status = value)
                                    },
                                    role = Role.RadioButton
                                )
                                .padding(vertical = 8.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            RadioButton(
                                selected = currentFilters.status == value,
                                onClick = null
                            )
                            Spacer(modifier = Modifier.width(8.dp))
                            Text(text = label)
                        }
                    }
                }
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // Year Section
            FilterSection(
                title = "Year"
            ) {
                val currentYear = 2024
                val years = (currentYear downTo 1960).toList()
                
                LazyRow(
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    item {
                        FilterChip(
                            selected = currentFilters.year == null,
                            onClick = {
                                currentFilters = currentFilters.copy(year = null)
                            },
                            label = { Text("All Years") }
                        )
                    }
                    
                    items(years.take(20)) { year ->
                        FilterChip(
                            selected = currentFilters.year == year,
                            onClick = {
                                currentFilters = currentFilters.copy(
                                    year = if (currentFilters.year == year) null else year
                                )
                            },
                            label = { Text(year.toString()) }
                        )
                    }
                }
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // Score Range Section
            FilterSection(
                title = "Minimum Score"
            ) {
                val scoreOptions = listOf(
                    null to "Any",
                    5.0 to "5.0+",
                    6.0 to "6.0+",
                    7.0 to "7.0+",
                    8.0 to "8.0+",
                    9.0 to "9.0+"
                )
                
                LazyRow(
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    items(scoreOptions) { (score, label) ->
                        FilterChip(
                            selected = currentFilters.minScore == score,
                            onClick = {
                                currentFilters = currentFilters.copy(
                                    minScore = if (currentFilters.minScore == score) null else score
                                )
                            },
                            label = { Text(label) }
                        )
                    }
                }
            }
            
            Spacer(modifier = Modifier.height(32.dp))
        }
    }
}

@Composable
fun FilterSection(
    title: String,
    content: @Composable () -> Unit
) {
    Column {
        Text(
            text = title,
            style = MaterialTheme.typography.titleMedium,
            fontWeight = FontWeight.SemiBold,
            color = MaterialTheme.colorScheme.onSurface
        )
        
        Spacer(modifier = Modifier.height(8.dp))
        
        content()
    }
}
