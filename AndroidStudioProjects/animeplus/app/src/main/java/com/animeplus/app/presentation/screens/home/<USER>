package com.animeplus.app.presentation.screens.home

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Refresh
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.animeplus.app.data.model.Anime
import com.animeplus.app.presentation.components.*
import com.animeplus.app.presentation.viewmodel.HomeViewModel
import com.google.accompanist.swiperefresh.SwipeRefresh
import com.google.accompanist.swiperefresh.rememberSwipeRefreshState

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun HomeScreen(
    onAnimeClick: (Int) -> Unit,
    viewModel: HomeViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsState()
    val isRefreshing by viewModel.isRefreshing.collectAsState()
    
    LaunchedEffect(Unit) {
        viewModel.loadHomeData()
    }
    
    Column(
        modifier = Modifier.fillMaxSize()
    ) {
        // Top App Bar
        TopAppBar(
            title = {
                Text(
                    text = "Anime Plus",
                    style = MaterialTheme.typography.headlineMedium,
                    fontWeight = FontWeight.Bold
                )
            },
            actions = {
                IconButton(onClick = { viewModel.refresh() }) {
                    Icon(
                        imageVector = Icons.Default.Refresh,
                        contentDescription = "Refresh"
                    )
                }
            }
        )
        
        SwipeRefresh(
            state = rememberSwipeRefreshState(isRefreshing),
            onRefresh = { viewModel.refresh() }
        ) {
            LazyColumn(
                modifier = Modifier.fillMaxSize(),
                contentPadding = PaddingValues(vertical = 16.dp),
                verticalArrangement = Arrangement.spacedBy(24.dp)
            ) {
                // Error handling
                uiState.error?.let { error ->
                    item {
                        ErrorCard(
                            message = error,
                            onRetry = { viewModel.loadHomeData() },
                            onDismiss = { viewModel.clearError() }
                        )
                    }
                }
                
                // Loading state
                if (uiState.isLoading && uiState.topAnime.isEmpty()) {
                    items(3) {
                        AnimeRowSkeleton()
                    }
                } else {
                    // Featured/Top Anime Carousel
                    if (uiState.topAnime.isNotEmpty()) {
                        item {
                            Column {
                                SectionHeader(
                                    title = "🔥 Trending Now",
                                    subtitle = "Most popular anime"
                                )
                                Spacer(modifier = Modifier.height(12.dp))
                                FeaturedAnimeCarousel(
                                    animeList = uiState.topAnime.take(5),
                                    onAnimeClick = onAnimeClick,
                                    onFavoriteClick = { anime ->
                                        if (anime.isFavorite) {
                                            viewModel.removeFromFavorites(anime.malId)
                                        } else {
                                            viewModel.addToFavorites(anime)
                                        }
                                    }
                                )
                            }
                        }
                    }
                    
                    // Recently Watched
                    if (uiState.recentlyWatched.isNotEmpty()) {
                        item {
                            AnimeSection(
                                title = "📺 Continue Watching",
                                subtitle = "Pick up where you left off",
                                animeList = uiState.recentlyWatched,
                                onAnimeClick = onAnimeClick,
                                onSeeAllClick = { /* Navigate to continue watching */ }
                            )
                        }
                    }
                    
                    // Current Season
                    if (uiState.currentSeasonAnime.isNotEmpty()) {
                        item {
                            AnimeSection(
                                title = "🌸 This Season",
                                subtitle = "Currently airing anime",
                                animeList = uiState.currentSeasonAnime,
                                onAnimeClick = onAnimeClick,
                                onSeeAllClick = { /* Navigate to current season */ }
                            )
                        }
                    }
                    
                    // Popular Streaming
                    if (uiState.popularStreaming.isNotEmpty()) {
                        item {
                            StreamingAnimeSection(
                                title = "⭐ Popular Now",
                                subtitle = "Most watched this week",
                                animeList = uiState.popularStreaming,
                                onAnimeClick = { streamingAnime ->
                                    // Convert streaming anime to regular anime click
                                    // This would need proper mapping
                                }
                            )
                        }
                    }
                    
                    // Recent Episodes
                    if (uiState.recentEpisodes.isNotEmpty()) {
                        item {
                            RecentEpisodesSection(
                                title = "🆕 Latest Episodes",
                                subtitle = "Fresh episodes just released",
                                episodes = uiState.recentEpisodes,
                                onEpisodeClick = { episode ->
                                    // Handle episode click
                                }
                            )
                        }
                    }
                    
                    // Upcoming Anime
                    if (uiState.upcomingAnime.isNotEmpty()) {
                        item {
                            AnimeSection(
                                title = "🔮 Coming Soon",
                                subtitle = "Upcoming anime releases",
                                animeList = uiState.upcomingAnime,
                                onAnimeClick = onAnimeClick,
                                onSeeAllClick = { /* Navigate to upcoming */ }
                            )
                        }
                    }
                }
            }
        }
    }
}

@Composable
fun AnimeSection(
    title: String,
    subtitle: String,
    animeList: List<Anime>,
    onAnimeClick: (Int) -> Unit,
    onSeeAllClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Column(modifier = modifier) {
        SectionHeader(
            title = title,
            subtitle = subtitle,
            onSeeAllClick = onSeeAllClick
        )
        Spacer(modifier = Modifier.height(12.dp))
        LazyRow(
            horizontalArrangement = Arrangement.spacedBy(12.dp),
            contentPadding = PaddingValues(horizontal = 16.dp)
        ) {
            items(animeList) { anime ->
                AnimeCard(
                    anime = anime,
                    onClick = { onAnimeClick(anime.malId) }
                )
            }
        }
    }
}

@Composable
fun StreamingAnimeSection(
    title: String,
    subtitle: String,
    animeList: List<com.animeplus.app.data.model.StreamingAnime>,
    onAnimeClick: (com.animeplus.app.data.model.StreamingAnime) -> Unit,
    modifier: Modifier = Modifier
) {
    Column(modifier = modifier) {
        SectionHeader(
            title = title,
            subtitle = subtitle
        )
        Spacer(modifier = Modifier.height(12.dp))
        LazyRow(
            horizontalArrangement = Arrangement.spacedBy(12.dp),
            contentPadding = PaddingValues(horizontal = 16.dp)
        ) {
            items(animeList) { anime ->
                StreamingAnimeCard(
                    anime = anime,
                    onClick = { onAnimeClick(anime) }
                )
            }
        }
    }
}

@Composable
fun RecentEpisodesSection(
    title: String,
    subtitle: String,
    episodes: List<com.animeplus.app.data.model.RecentEpisode>,
    onEpisodeClick: (com.animeplus.app.data.model.RecentEpisode) -> Unit,
    modifier: Modifier = Modifier
) {
    Column(modifier = modifier) {
        SectionHeader(
            title = title,
            subtitle = subtitle
        )
        Spacer(modifier = Modifier.height(12.dp))
        LazyRow(
            horizontalArrangement = Arrangement.spacedBy(12.dp),
            contentPadding = PaddingValues(horizontal = 16.dp)
        ) {
            items(episodes) { episode ->
                EpisodeCard(
                    episode = episode,
                    onClick = { onEpisodeClick(episode) }
                )
            }
        }
    }
}

@Composable
fun SectionHeader(
    title: String,
    subtitle: String? = null,
    onSeeAllClick: (() -> Unit)? = null,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Column(modifier = Modifier.weight(1f)) {
            Text(
                text = title,
                style = MaterialTheme.typography.titleLarge,
                fontWeight = FontWeight.Bold
            )
            subtitle?.let {
                Text(
                    text = it,
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
        
        onSeeAllClick?.let {
            TextButton(onClick = it) {
                Text("See All")
            }
        }
    }
}
