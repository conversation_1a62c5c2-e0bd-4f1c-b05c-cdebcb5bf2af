package com.animeplus.app.presentation.screens.profile

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import coil.compose.AsyncImage
import com.animeplus.app.presentation.components.*
import com.animeplus.app.presentation.viewmodel.ProfileViewModel

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ProfileScreen(
    onLoginClick: () -> Unit,
    viewModel: ProfileViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsState()
    
    LaunchedEffect(Unit) {
        viewModel.loadProfile()
    }
    
    LazyColumn(
        modifier = Modifier.fillMaxSize(),
        contentPadding = PaddingValues(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        item {
            // Profile Header
            if (uiState.user != null) {
                UserProfileHeader(
                    user = uiState.user,
                    stats = uiState.stats
                )
            } else {
                GuestProfileHeader(onLoginClick = onLoginClick)
            }
        }
        
        if (uiState.user != null) {
            item {
                // Quick Stats
                QuickStatsSection(stats = uiState.stats)
            }
            
            item {
                // Settings Section
                SettingsSection(
                    onThemeToggle = { viewModel.toggleTheme() },
                    onNotificationsToggle = { viewModel.toggleNotifications() },
                    onLanguageClick = { /* Handle language selection */ },
                    onQualityClick = { /* Handle quality selection */ }
                )
            }
            
            item {
                // Account Section
                AccountSection(
                    onEditProfile = { /* Handle edit profile */ },
                    onChangePassword = { /* Handle change password */ },
                    onPrivacy = { /* Handle privacy settings */ },
                    onSignOut = { viewModel.signOut() }
                )
            }
        }
        
        item {
            // App Info Section
            AppInfoSection(
                onAbout = { /* Handle about */ },
                onHelp = { /* Handle help */ },
                onFeedback = { /* Handle feedback */ },
                onRateApp = { /* Handle rate app */ }
            )
        }
    }
}

@Composable
fun UserProfileHeader(
    user: com.animeplus.app.data.model.User,
    stats: com.animeplus.app.data.model.UserStats?,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.primaryContainer
        )
    ) {
        Column(
            modifier = Modifier.padding(20.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            // Profile Picture
            AsyncImage(
                model = user.photoUrl ?: "https://via.placeholder.com/100",
                contentDescription = "Profile Picture",
                modifier = Modifier
                    .size(80.dp)
                    .clip(CircleShape),
                contentScale = ContentScale.Crop
            )
            
            Spacer(modifier = Modifier.height(12.dp))
            
            // User Info
            Text(
                text = user.displayName ?: "User",
                style = MaterialTheme.typography.headlineSmall,
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.colorScheme.onPrimaryContainer
            )
            
            Text(
                text = user.email,
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onPrimaryContainer.copy(alpha = 0.7f)
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // Quick Stats
            stats?.let {
                Row(
                    horizontalArrangement = Arrangement.spacedBy(24.dp)
                ) {
                    StatItem(
                        label = "Watched",
                        value = it.totalAnimeWatched.toString()
                    )
                    StatItem(
                        label = "Episodes",
                        value = it.totalEpisodesWatched.toString()
                    )
                    StatItem(
                        label = "Hours",
                        value = "${it.totalWatchTime / 3600000}h"
                    )
                }
            }
        }
    }
}

@Composable
fun GuestProfileHeader(
    onLoginClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surfaceVariant
        )
    ) {
        Column(
            modifier = Modifier.padding(20.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Icon(
                imageVector = Icons.Default.Person,
                contentDescription = null,
                modifier = Modifier.size(80.dp),
                tint = MaterialTheme.colorScheme.onSurfaceVariant
            )
            
            Spacer(modifier = Modifier.height(12.dp))
            
            Text(
                text = "Welcome to Anime Plus",
                style = MaterialTheme.typography.headlineSmall,
                fontWeight = FontWeight.Bold
            )
            
            Text(
                text = "Sign in to sync your favorites and watch history",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            Button(
                onClick = onLoginClick,
                modifier = Modifier.fillMaxWidth()
            ) {
                Text("Sign In")
            }
        }
    }
}

@Composable
fun StatItem(
    label: String,
    value: String,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = value,
            style = MaterialTheme.typography.titleLarge,
            fontWeight = FontWeight.Bold,
            color = MaterialTheme.colorScheme.onPrimaryContainer
        )
        Text(
            text = label,
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onPrimaryContainer.copy(alpha = 0.7f)
        )
    }
}

@Composable
fun QuickStatsSection(
    stats: com.animeplus.app.data.model.UserStats?,
    modifier: Modifier = Modifier
) {
    Card(modifier = modifier.fillMaxWidth()) {
        Column(modifier = Modifier.padding(16.dp)) {
            Text(
                text = "Your Stats",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.SemiBold
            )
            
            Spacer(modifier = Modifier.height(12.dp))
            
            stats?.let {
                Column(verticalArrangement = Arrangement.spacedBy(8.dp)) {
                    StatRow(
                        icon = Icons.Default.Tv,
                        label = "Total Anime Watched",
                        value = it.totalAnimeWatched.toString()
                    )
                    StatRow(
                        icon = Icons.Default.PlayArrow,
                        label = "Episodes Watched",
                        value = it.totalEpisodesWatched.toString()
                    )
                    StatRow(
                        icon = Icons.Default.Schedule,
                        label = "Watch Time",
                        value = "${it.totalWatchTime / 3600000} hours"
                    )
                    if (it.averageScore > 0) {
                        StatRow(
                            icon = Icons.Default.Star,
                            label = "Average Score",
                            value = String.format("%.1f", it.averageScore)
                        )
                    }
                }
            }
        }
    }
}

@Composable
fun SettingsSection(
    onThemeToggle: () -> Unit,
    onNotificationsToggle: () -> Unit,
    onLanguageClick: () -> Unit,
    onQualityClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(modifier = modifier.fillMaxWidth()) {
        Column(modifier = Modifier.padding(16.dp)) {
            Text(
                text = "Settings",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.SemiBold
            )
            
            Spacer(modifier = Modifier.height(12.dp))
            
            SettingItem(
                icon = Icons.Default.DarkMode,
                title = "Dark Theme",
                subtitle = "Toggle dark/light theme",
                onClick = onThemeToggle,
                trailing = {
                    Switch(
                        checked = true, // Get from theme state
                        onCheckedChange = { onThemeToggle() }
                    )
                }
            )
            
            SettingItem(
                icon = Icons.Default.Notifications,
                title = "Notifications",
                subtitle = "Episode updates and reminders",
                onClick = onNotificationsToggle,
                trailing = {
                    Switch(
                        checked = true, // Get from user preferences
                        onCheckedChange = { onNotificationsToggle() }
                    )
                }
            )
            
            SettingItem(
                icon = Icons.Default.Language,
                title = "Language",
                subtitle = "App language",
                onClick = onLanguageClick
            )
            
            SettingItem(
                icon = Icons.Default.HighQuality,
                title = "Video Quality",
                subtitle = "Default streaming quality",
                onClick = onQualityClick
            )
        }
    }
}

@Composable
fun AccountSection(
    onEditProfile: () -> Unit,
    onChangePassword: () -> Unit,
    onPrivacy: () -> Unit,
    onSignOut: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(modifier = modifier.fillMaxWidth()) {
        Column(modifier = Modifier.padding(16.dp)) {
            Text(
                text = "Account",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.SemiBold
            )
            
            Spacer(modifier = Modifier.height(12.dp))
            
            SettingItem(
                icon = Icons.Default.Edit,
                title = "Edit Profile",
                subtitle = "Update your profile information",
                onClick = onEditProfile
            )
            
            SettingItem(
                icon = Icons.Default.Lock,
                title = "Change Password",
                subtitle = "Update your password",
                onClick = onChangePassword
            )
            
            SettingItem(
                icon = Icons.Default.Security,
                title = "Privacy & Security",
                subtitle = "Manage your privacy settings",
                onClick = onPrivacy
            )
            
            SettingItem(
                icon = Icons.Default.Logout,
                title = "Sign Out",
                subtitle = "Sign out of your account",
                onClick = onSignOut,
                textColor = MaterialTheme.colorScheme.error
            )
        }
    }
}

@Composable
fun AppInfoSection(
    onAbout: () -> Unit,
    onHelp: () -> Unit,
    onFeedback: () -> Unit,
    onRateApp: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(modifier = modifier.fillMaxWidth()) {
        Column(modifier = Modifier.padding(16.dp)) {
            Text(
                text = "App Info",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.SemiBold
            )
            
            Spacer(modifier = Modifier.height(12.dp))
            
            SettingItem(
                icon = Icons.Default.Info,
                title = "About",
                subtitle = "App version and information",
                onClick = onAbout
            )
            
            SettingItem(
                icon = Icons.Default.Help,
                title = "Help & Support",
                subtitle = "Get help and contact support",
                onClick = onHelp
            )
            
            SettingItem(
                icon = Icons.Default.Feedback,
                title = "Send Feedback",
                subtitle = "Help us improve the app",
                onClick = onFeedback
            )
            
            SettingItem(
                icon = Icons.Default.Star,
                title = "Rate App",
                subtitle = "Rate us on the Play Store",
                onClick = onRateApp
            )
        }
    }
}

@Composable
fun StatRow(
    icon: androidx.compose.ui.graphics.vector.ImageVector,
    label: String,
    value: String,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier.fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Icon(
            imageVector = icon,
            contentDescription = null,
            modifier = Modifier.size(20.dp),
            tint = MaterialTheme.colorScheme.primary
        )
        
        Spacer(modifier = Modifier.width(12.dp))
        
        Text(
            text = label,
            style = MaterialTheme.typography.bodyMedium,
            modifier = Modifier.weight(1f)
        )
        
        Text(
            text = value,
            style = MaterialTheme.typography.bodyMedium,
            fontWeight = FontWeight.SemiBold
        )
    }
}

@Composable
fun SettingItem(
    icon: androidx.compose.ui.graphics.vector.ImageVector,
    title: String,
    subtitle: String,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    trailing: @Composable (() -> Unit)? = null,
    textColor: androidx.compose.ui.graphics.Color = MaterialTheme.colorScheme.onSurface
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .clickable { onClick() }
            .padding(vertical = 8.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Icon(
            imageVector = icon,
            contentDescription = null,
            modifier = Modifier.size(24.dp),
            tint = textColor
        )
        
        Spacer(modifier = Modifier.width(16.dp))
        
        Column(modifier = Modifier.weight(1f)) {
            Text(
                text = title,
                style = MaterialTheme.typography.bodyLarge,
                color = textColor
            )
            Text(
                text = subtitle,
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
        
        trailing?.invoke() ?: Icon(
            imageVector = Icons.Default.ChevronRight,
            contentDescription = null,
            tint = MaterialTheme.colorScheme.onSurfaceVariant
        )
    }
}

// Placeholder ViewModel
@androidx.lifecycle.ViewModel
class ProfileViewModel @javax.inject.Inject constructor() : androidx.lifecycle.ViewModel() {
    
    private val _uiState = kotlinx.coroutines.flow.MutableStateFlow(ProfileUiState())
    val uiState: kotlinx.coroutines.flow.StateFlow<ProfileUiState> = _uiState.asStateFlow()
    
    fun loadProfile() {
        // Implementation
    }
    
    fun toggleTheme() {
        // Implementation
    }
    
    fun toggleNotifications() {
        // Implementation
    }
    
    fun signOut() {
        // Implementation
    }
}

data class ProfileUiState(
    val isLoading: Boolean = false,
    val user: com.animeplus.app.data.model.User? = null,
    val stats: com.animeplus.app.data.model.UserStats? = null,
    val error: String? = null
)
