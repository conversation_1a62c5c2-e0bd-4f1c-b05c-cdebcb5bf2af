package com.animeplus.app.presentation.screens.search

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.lazy.grid.rememberLazyGridState
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Clear
import androidx.compose.material.icons.filled.FilterList
import androidx.compose.material.icons.filled.Search
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.animeplus.app.presentation.components.*
import com.animeplus.app.presentation.viewmodel.SearchViewModel

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SearchScreen(
    onAnimeClick: (Int) -> Unit,
    viewModel: SearchViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsState()
    val searchQuery by viewModel.searchQuery.collectAsState()
    val filters by viewModel.filters.collectAsState()
    val keyboardController = LocalSoftwareKeyboardController.current
    val gridState = rememberLazyGridState()
    
    var showFilters by remember { mutableStateOf(false) }
    
    // Infinite scroll
    LaunchedEffect(gridState) {
        snapshotFlow { gridState.layoutInfo.visibleItemsInfo.lastOrNull()?.index }
            .collect { lastVisibleIndex ->
                if (lastVisibleIndex != null && 
                    lastVisibleIndex >= uiState.searchResults.size - 5 && 
                    uiState.hasNextPage && 
                    !uiState.isLoading) {
                    viewModel.loadNextPage()
                }
            }
    }
    
    Column(
        modifier = Modifier.fillMaxSize()
    ) {
        // Top App Bar with Search
        TopAppBar(
            title = {
                OutlinedTextField(
                    value = searchQuery,
                    onValueChange = viewModel::updateSearchQuery,
                    placeholder = { Text("Search anime...") },
                    leadingIcon = {
                        Icon(
                            imageVector = Icons.Default.Search,
                            contentDescription = "Search"
                        )
                    },
                    trailingIcon = {
                        Row {
                            if (searchQuery.isNotEmpty()) {
                                IconButton(
                                    onClick = { viewModel.clearResults() }
                                ) {
                                    Icon(
                                        imageVector = Icons.Default.Clear,
                                        contentDescription = "Clear"
                                    )
                                }
                            }
                            
                            IconButton(
                                onClick = { showFilters = true }
                            ) {
                                Icon(
                                    imageVector = Icons.Default.FilterList,
                                    contentDescription = "Filters"
                                )
                            }
                        }
                    },
                    singleLine = true,
                    keyboardOptions = KeyboardOptions(
                        imeAction = ImeAction.Search
                    ),
                    keyboardActions = KeyboardActions(
                        onSearch = {
                            keyboardController?.hide()
                            viewModel.searchAnime(searchQuery)
                        }
                    ),
                    modifier = Modifier.fillMaxWidth()
                )
            }
        )
        
        // Content
        Box(modifier = Modifier.fillMaxSize()) {
            when {
                // Empty state
                searchQuery.isEmpty() -> {
                    SearchEmptyState()
                }
                
                // Loading state
                uiState.isLoading && uiState.searchResults.isEmpty() -> {
                    FullScreenLoading("Searching anime...")
                }
                
                // Error state
                uiState.error != null && uiState.searchResults.isEmpty() -> {
                    FullScreenError(
                        title = "Search Failed",
                        message = uiState.error,
                        onRetry = { viewModel.searchAnime(searchQuery) }
                    )
                }
                
                // Results
                uiState.searchResults.isNotEmpty() -> {
                    LazyVerticalGrid(
                        columns = GridCells.Fixed(2),
                        state = gridState,
                        contentPadding = PaddingValues(16.dp),
                        horizontalArrangement = Arrangement.spacedBy(12.dp),
                        verticalArrangement = Arrangement.spacedBy(12.dp)
                    ) {
                        items(uiState.searchResults) { anime ->
                            AnimeCard(
                                anime = anime,
                                onClick = { onAnimeClick(anime.malId) }
                            )
                        }
                        
                        // Loading more indicator
                        if (uiState.isLoading && uiState.hasNextPage) {
                            item {
                                Box(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .padding(16.dp),
                                    contentAlignment = Alignment.Center
                                ) {
                                    CircularProgressIndicator()
                                }
                            }
                        }
                    }
                }
                
                // No results
                else -> {
                    EmptyState(
                        title = "No Results Found",
                        message = "Try searching with different keywords or adjust your filters.",
                        actionText = "Clear Filters",
                        onAction = { viewModel.clearFilters() },
                        icon = Icons.Default.Search
                    )
                }
            }
            
            // Error snackbar
            uiState.error?.let { error ->
                if (uiState.searchResults.isNotEmpty()) {
                    LaunchedEffect(error) {
                        // Show snackbar for errors when results exist
                    }
                }
            }
        }
    }
    
    // Filter bottom sheet
    if (showFilters) {
        SearchFiltersBottomSheet(
            filters = filters,
            onFiltersChanged = viewModel::updateFilters,
            onDismiss = { showFilters = false }
        )
    }
}

@Composable
fun SearchEmptyState() {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(32.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        Icon(
            imageVector = Icons.Default.Search,
            contentDescription = null,
            tint = MaterialTheme.colorScheme.onSurfaceVariant,
            modifier = Modifier.size(64.dp)
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        Text(
            text = "Search for Anime",
            style = MaterialTheme.typography.headlineSmall,
            color = MaterialTheme.colorScheme.onSurface
        )
        
        Spacer(modifier = Modifier.height(8.dp))
        
        Text(
            text = "Find your favorite anime by title, genre, or year",
            style = MaterialTheme.typography.bodyLarge,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
        
        Spacer(modifier = Modifier.height(24.dp))
        
        // Popular search suggestions
        Column {
            Text(
                text = "Popular Searches:",
                style = MaterialTheme.typography.titleMedium,
                color = MaterialTheme.colorScheme.onSurface
            )
            
            Spacer(modifier = Modifier.height(12.dp))
            
            val popularSearches = listOf(
                "Attack on Titan",
                "Demon Slayer",
                "One Piece",
                "Naruto",
                "Dragon Ball"
            )
            
            popularSearches.forEach { search ->
                SuggestionChip(
                    onClick = { /* Handle suggestion click */ },
                    label = { Text(search) },
                    modifier = Modifier.padding(vertical = 2.dp)
                )
            }
        }
    }
}
