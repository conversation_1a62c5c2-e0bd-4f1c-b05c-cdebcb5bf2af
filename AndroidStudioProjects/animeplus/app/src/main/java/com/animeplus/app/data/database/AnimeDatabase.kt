package com.animeplus.app.data.database

import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import androidx.room.TypeConverters
import androidx.room.migration.Migration
import androidx.sqlite.db.SupportSQLiteDatabase
import android.content.Context
import com.animeplus.app.data.model.*

@Database(
    entities = [
        Anime::class,
        Episode::class,
        FavoriteAnime::class,
        WatchHistory::class,
        User::class
    ],
    version = 1,
    exportSchema = false
)
@TypeConverters(Converters::class)
abstract class AnimeDatabase : RoomDatabase() {
    
    abstract fun animeDao(): AnimeDao
    
    companion object {
        @Volatile
        private var INSTANCE: AnimeDatabase? = null
        
        private const val DATABASE_NAME = "anime_database"
        
        fun getDatabase(context: Context): AnimeDatabase {
            return INSTANCE ?: synchronized(this) {
                val instance = Room.databaseBuilder(
                    context.applicationContext,
                    AnimeDatabase::class.java,
                    DATABASE_NAME
                )
                    .addMigrations(MIGRATION_1_2)
                    .fallbackToDestructiveMigration()
                    .build()
                INSTANCE = instance
                instance
            }
        }
        
        // Migration example for future database updates
        private val MIGRATION_1_2 = object : Migration(1, 2) {
            override fun migrate(database: SupportSQLiteDatabase) {
                // Example migration - add new column
                // database.execSQL("ALTER TABLE anime ADD COLUMN newColumn TEXT")
            }
        }
        
        // Pre-populate database with sample data (optional)
        private val CALLBACK = object : RoomDatabase.Callback() {
            override fun onCreate(db: SupportSQLiteDatabase) {
                super.onCreate(db)
                // Pre-populate database if needed
                // You can add initial data here
            }
        }
    }
}

// Database utilities
object DatabaseUtils {
    
    suspend fun clearAllData(database: AnimeDatabase) {
        val dao = database.animeDao()
        dao.clearAllAnime()
        dao.clearAllWatchHistory()
        dao.clearAllUsers()
    }
    
    suspend fun exportUserData(database: AnimeDatabase, userId: String): UserDataExport? {
        val dao = database.animeDao()
        val user = dao.getUserById(userId) ?: return null
        val favorites = dao.getAllFavorites()
        val watchHistory = dao.getAllWatchHistory()
        
        return UserDataExport(
            user = user,
            favorites = favorites.toString(), // Convert Flow to List in actual implementation
            watchHistory = watchHistory.toString() // Convert Flow to List in actual implementation
        )
    }
    
    suspend fun importUserData(database: AnimeDatabase, userDataExport: UserDataExport) {
        val dao = database.animeDao()
        dao.insertUser(userDataExport.user)
        // Import favorites and watch history
        // Implementation depends on how you handle the Flow conversion
    }
}

data class UserDataExport(
    val user: User,
    val favorites: String, // JSON string of favorites
    val watchHistory: String // JSON string of watch history
)

// Database health check
class DatabaseHealthChecker(private val database: AnimeDatabase) {
    
    suspend fun checkDatabaseHealth(): DatabaseHealth {
        return try {
            val dao = database.animeDao()
            val animeCount = dao.getAnimeCount()
            val favoritesCount = dao.getFavoritesCount()
            val watchHistoryCount = dao.getWatchHistoryCount()
            
            DatabaseHealth(
                isHealthy = true,
                animeCount = animeCount,
                favoritesCount = favoritesCount,
                watchHistoryCount = watchHistoryCount,
                lastChecked = System.currentTimeMillis()
            )
        } catch (e: Exception) {
            DatabaseHealth(
                isHealthy = false,
                error = e.message,
                lastChecked = System.currentTimeMillis()
            )
        }
    }
}

data class DatabaseHealth(
    val isHealthy: Boolean,
    val animeCount: Int = 0,
    val favoritesCount: Int = 0,
    val watchHistoryCount: Int = 0,
    val error: String? = null,
    val lastChecked: Long
)
