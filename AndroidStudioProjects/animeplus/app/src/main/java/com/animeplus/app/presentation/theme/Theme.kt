package com.animeplus.app.presentation.theme

import android.app.Activity
import android.os.Build
import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.darkColorScheme
import androidx.compose.material3.dynamicDarkColorScheme
import androidx.compose.material3.dynamicLightColorScheme
import androidx.compose.material3.lightColorScheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.SideEffect
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalView
import androidx.core.view.WindowCompat

private val DarkColorScheme = darkColorScheme(
    primary = PrimaryRed,
    onPrimary = TextPrimary,
    primaryContainer = PrimaryRedDark,
    onPrimaryContainer = TextPrimary,
    
    secondary = SecondaryBlue,
    onSecondary = TextPrimary,
    secondaryContainer = SecondaryBlueDark,
    onSecondaryContainer = TextPrimary,
    
    tertiary = AccentPurple,
    onTertiary = TextPrimary,
    tertiaryContainer = AccentPurple,
    onTertiaryContainer = TextPrimary,
    
    background = BackgroundDark,
    onBackground = TextPrimary,
    
    surface = SurfaceDark,
    onSurface = TextPrimary,
    surfaceVariant = SurfaceDarkSecondary,
    onSurfaceVariant = TextSecondary,
    
    surfaceTint = PrimaryRed,
    inverseSurface = SurfaceLight,
    inverseOnSurface = TextPrimaryLight,
    
    error = ErrorRed,
    onError = TextPrimary,
    errorContainer = ErrorRed,
    onErrorContainer = TextPrimary,
    
    outline = BorderDark,
    outlineVariant = BorderDark,
    
    scrim = BackgroundDark
)

private val LightColorScheme = lightColorScheme(
    primary = PrimaryRed,
    onPrimary = TextPrimary,
    primaryContainer = PrimaryRedLight,
    onPrimaryContainer = TextPrimaryLight,
    
    secondary = SecondaryBlue,
    onSecondary = TextPrimary,
    secondaryContainer = SecondaryBlueLight,
    onSecondaryContainer = TextPrimaryLight,
    
    tertiary = AccentPurple,
    onTertiary = TextPrimary,
    tertiaryContainer = AccentPurple,
    onTertiaryContainer = TextPrimary,
    
    background = BackgroundLight,
    onBackground = TextPrimaryLight,
    
    surface = SurfaceLight,
    onSurface = TextPrimaryLight,
    surfaceVariant = SurfaceLightSecondary,
    onSurfaceVariant = TextSecondaryLight,
    
    surfaceTint = PrimaryRed,
    inverseSurface = SurfaceDark,
    inverseOnSurface = TextPrimary,
    
    error = ErrorRed,
    onError = TextPrimary,
    errorContainer = ErrorRed,
    onErrorContainer = TextPrimary,
    
    outline = BorderLight,
    outlineVariant = BorderLight,
    
    scrim = BackgroundLight
)

@Composable
fun AnimePlusTheme(
    darkTheme: Boolean = isSystemInDarkTheme(),
    // Dynamic color is available on Android 12+
    dynamicColor: Boolean = false,
    content: @Composable () -> Unit
) {
    val colorScheme = when {
        dynamicColor && Build.VERSION.SDK_INT >= Build.VERSION_CODES.S -> {
            val context = LocalContext.current
            if (darkTheme) dynamicDarkColorScheme(context) else dynamicLightColorScheme(context)
        }

        darkTheme -> DarkColorScheme
        else -> LightColorScheme
    }
    
    val view = LocalView.current
    if (!view.isInEditMode) {
        SideEffect {
            val window = (view.context as Activity).window
            window.statusBarColor = colorScheme.background.toArgb()
            window.navigationBarColor = colorScheme.background.toArgb()
            WindowCompat.getInsetsController(window, view).isAppearanceLightStatusBars = !darkTheme
            WindowCompat.getInsetsController(window, view).isAppearanceLightNavigationBars = !darkTheme
        }
    }

    MaterialTheme(
        colorScheme = colorScheme,
        typography = Typography,
        content = content
    )
}
