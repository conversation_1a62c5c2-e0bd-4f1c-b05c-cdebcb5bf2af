package com.animeplus.app.data.repository

import com.animeplus.app.data.database.AnimeDao
import com.animeplus.app.data.model.*
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.auth.FirebaseUser
import com.google.firebase.firestore.FirebaseFirestore
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.tasks.await
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class UserRepository @Inject constructor(
    private val firebaseAuth: FirebaseAuth,
    private val firestore: FirebaseFirestore,
    private val animeDao: AnimeDao
) {
    
    // Authentication operations
    suspend fun signInWithEmailAndPassword(email: String, password: String): Result<User> {
        return try {
            val authResult = firebaseAuth.signInWithEmailAndPassword(email, password).await()
            val firebaseUser = authResult.user
            
            if (firebaseUser != null) {
                val user = firebaseUser.toUser()
                // Save user to local database
                animeDao.insertUser(user)
                // Sync user data from cloud
                syncUserDataFromCloud(user.uid)
                Result.success(user)
            } else {
                Result.failure(Exception("Authentication failed"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    suspend fun createUserWithEmailAndPassword(
        email: String, 
        password: String, 
        displayName: String
    ): Result<User> {
        return try {
            val authResult = firebaseAuth.createUserWithEmailAndPassword(email, password).await()
            val firebaseUser = authResult.user
            
            if (firebaseUser != null) {
                // Update display name
                val profileUpdates = com.google.firebase.auth.UserProfileChangeRequest.Builder()
                    .setDisplayName(displayName)
                    .build()
                firebaseUser.updateProfile(profileUpdates).await()
                
                val user = firebaseUser.toUser().copy(displayName = displayName)
                
                // Save user to local database
                animeDao.insertUser(user)
                
                // Create user document in Firestore
                createUserDocument(user)
                
                Result.success(user)
            } else {
                Result.failure(Exception("User creation failed"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    suspend fun signOut(): Result<Unit> {
        return try {
            firebaseAuth.signOut()
            // Clear local user data
            animeDao.clearAllUsers()
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    suspend fun getCurrentUser(): User? {
        val firebaseUser = firebaseAuth.currentUser
        return if (firebaseUser != null) {
            animeDao.getUserById(firebaseUser.uid) ?: firebaseUser.toUser()
        } else {
            null
        }
    }
    
    suspend fun updateUserProfile(user: User): Result<User> {
        return try {
            // Update in local database
            animeDao.updateUser(user)
            
            // Update in Firestore
            firestore.collection("users")
                .document(user.uid)
                .set(user)
                .await()
            
            Result.success(user)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    // Favorites operations
    fun getFavorites(): Flow<List<FavoriteAnime>> = animeDao.getAllFavorites()
    
    suspend fun addToFavorites(anime: Anime): Result<Unit> {
        return try {
            val favorite = FavoriteAnime(
                animeId = anime.malId,
                title = anime.title,
                imageUrl = anime.images?.jpg?.imageUrl,
                score = anime.score,
                episodes = anime.episodes,
                status = anime.status
            )
            
            // Add to local database
            animeDao.insertFavorite(favorite)
            
            // Sync to cloud if user is logged in
            getCurrentUser()?.let { user ->
                syncFavoriteToCloud(user.uid, favorite)
            }
            
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    suspend fun removeFromFavorites(animeId: Int): Result<Unit> {
        return try {
            // Remove from local database
            animeDao.deleteFavoriteById(animeId)
            
            // Remove from cloud if user is logged in
            getCurrentUser()?.let { user ->
                removeFavoriteFromCloud(user.uid, animeId)
            }
            
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    suspend fun isFavorite(animeId: Int): Boolean = animeDao.isFavorite(animeId)
    
    // Watch history operations
    fun getWatchHistory(): Flow<List<WatchHistory>> = animeDao.getAllWatchHistory()
    
    fun getWatchHistoryByAnime(animeId: Int): Flow<List<WatchHistory>> = 
        animeDao.getWatchHistoryByAnime(animeId)
    
    suspend fun addToWatchHistory(
        animeId: Int,
        episodeId: Int,
        episodeNumber: Int,
        animeTitle: String,
        episodeTitle: String? = null,
        thumbnailUrl: String? = null,
        watchProgress: Long = 0L,
        duration: Long = 0L
    ): Result<Unit> {
        return try {
            val watchHistory = WatchHistory(
                animeId = animeId,
                episodeId = episodeId,
                episodeNumber = episodeNumber,
                animeTitle = animeTitle,
                episodeTitle = episodeTitle,
                thumbnailUrl = thumbnailUrl,
                watchProgress = watchProgress,
                duration = duration,
                lastWatchedAt = System.currentTimeMillis()
            )
            
            // Add to local database
            animeDao.insertWatchHistory(watchHistory)
            
            // Sync to cloud if user is logged in
            getCurrentUser()?.let { user ->
                syncWatchHistoryToCloud(user.uid, watchHistory)
            }
            
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    suspend fun updateWatchProgress(
        animeId: Int,
        episodeId: Int,
        watchProgress: Long,
        duration: Long
    ): Result<Unit> {
        return try {
            val existingHistory = animeDao.getWatchHistoryByEpisode(animeId, episodeId)
            if (existingHistory != null) {
                val updatedHistory = existingHistory.copy(
                    watchProgress = watchProgress,
                    duration = duration,
                    lastWatchedAt = System.currentTimeMillis()
                )
                animeDao.updateWatchHistory(updatedHistory)
                
                // Sync to cloud
                getCurrentUser()?.let { user ->
                    syncWatchHistoryToCloud(user.uid, updatedHistory)
                }
            }
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    suspend fun clearWatchHistory(): Result<Unit> {
        return try {
            animeDao.clearAllWatchHistory()
            
            // Clear from cloud if user is logged in
            getCurrentUser()?.let { user ->
                clearWatchHistoryFromCloud(user.uid)
            }
            
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    // Cloud sync operations
    private suspend fun createUserDocument(user: User) {
        try {
            firestore.collection("users")
                .document(user.uid)
                .set(user)
                .await()
        } catch (e: Exception) {
            // Handle error silently for now
        }
    }
    
    private suspend fun syncUserDataFromCloud(userId: String) {
        try {
            // Sync favorites
            val favoritesSnapshot = firestore.collection("users")
                .document(userId)
                .collection("favorites")
                .get()
                .await()
            
            val cloudFavorites = favoritesSnapshot.documents.mapNotNull { doc ->
                doc.toObject(CloudFavorite::class.java)?.let { cloudFav ->
                    FavoriteAnime(
                        animeId = cloudFav.animeId,
                        title = cloudFav.title,
                        imageUrl = cloudFav.imageUrl,
                        addedAt = cloudFav.addedAt
                    )
                }
            }
            
            cloudFavorites.forEach { favorite ->
                animeDao.insertFavorite(favorite)
            }
            
            // Sync watch history
            val historySnapshot = firestore.collection("users")
                .document(userId)
                .collection("watchHistory")
                .get()
                .await()
            
            val cloudHistory = historySnapshot.documents.mapNotNull { doc ->
                doc.toObject(CloudWatchHistory::class.java)?.let { cloudHist ->
                    WatchHistory(
                        animeId = cloudHist.animeId,
                        episodeId = cloudHist.episodeId,
                        episodeNumber = cloudHist.episodeNumber,
                        animeTitle = cloudHist.animeTitle,
                        episodeTitle = cloudHist.episodeTitle,
                        watchProgress = cloudHist.watchProgress,
                        duration = cloudHist.duration,
                        lastWatchedAt = cloudHist.lastWatchedAt
                    )
                }
            }
            
            cloudHistory.forEach { history ->
                animeDao.insertWatchHistory(history)
            }
            
        } catch (e: Exception) {
            // Handle error silently for now
        }
    }
    
    private suspend fun syncFavoriteToCloud(userId: String, favorite: FavoriteAnime) {
        try {
            val cloudFavorite = CloudFavorite(
                animeId = favorite.animeId,
                title = favorite.title,
                imageUrl = favorite.imageUrl,
                addedAt = favorite.addedAt
            )
            
            firestore.collection("users")
                .document(userId)
                .collection("favorites")
                .document(favorite.animeId.toString())
                .set(cloudFavorite)
                .await()
        } catch (e: Exception) {
            // Handle error silently for now
        }
    }
    
    private suspend fun removeFavoriteFromCloud(userId: String, animeId: Int) {
        try {
            firestore.collection("users")
                .document(userId)
                .collection("favorites")
                .document(animeId.toString())
                .delete()
                .await()
        } catch (e: Exception) {
            // Handle error silently for now
        }
    }
    
    private suspend fun syncWatchHistoryToCloud(userId: String, watchHistory: WatchHistory) {
        try {
            val cloudHistory = CloudWatchHistory(
                animeId = watchHistory.animeId,
                episodeId = watchHistory.episodeId,
                episodeNumber = watchHistory.episodeNumber,
                animeTitle = watchHistory.animeTitle,
                episodeTitle = watchHistory.episodeTitle,
                watchProgress = watchHistory.watchProgress,
                duration = watchHistory.duration,
                lastWatchedAt = watchHistory.lastWatchedAt
            )
            
            firestore.collection("users")
                .document(userId)
                .collection("watchHistory")
                .document("${watchHistory.animeId}_${watchHistory.episodeId}")
                .set(cloudHistory)
                .await()
        } catch (e: Exception) {
            // Handle error silently for now
        }
    }
    
    private suspend fun clearWatchHistoryFromCloud(userId: String) {
        try {
            val historyCollection = firestore.collection("users")
                .document(userId)
                .collection("watchHistory")
            
            val documents = historyCollection.get().await()
            documents.documents.forEach { doc ->
                doc.reference.delete()
            }
        } catch (e: Exception) {
            // Handle error silently for now
        }
    }
}

// Extension function to convert FirebaseUser to User
private fun FirebaseUser.toUser(): User {
    return User(
        uid = uid,
        email = email ?: "",
        displayName = displayName,
        photoUrl = photoUrl?.toString(),
        isEmailVerified = isEmailVerified,
        createdAt = metadata?.creationTimestamp ?: System.currentTimeMillis(),
        lastLoginAt = metadata?.lastSignInTimestamp ?: System.currentTimeMillis()
    )
}
