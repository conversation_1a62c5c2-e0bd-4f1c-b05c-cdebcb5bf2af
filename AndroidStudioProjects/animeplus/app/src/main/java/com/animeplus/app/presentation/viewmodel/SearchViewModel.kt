package com.animeplus.app.presentation.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.animeplus.app.data.model.Anime
import com.animeplus.app.data.repository.AnimeRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.FlowPreview
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import javax.inject.Inject

@OptIn(FlowPreview::class)
@HiltViewModel
class SearchViewModel @Inject constructor(
    private val animeRepository: AnimeRepository
) : ViewModel() {
    
    private val _searchQuery = MutableStateFlow("")
    val searchQuery: StateFlow<String> = _searchQuery.asStateFlow()
    
    private val _uiState = MutableStateFlow(SearchUiState())
    val uiState: StateFlow<SearchUiState> = _uiState.asStateFlow()
    
    private val _filters = MutableStateFlow(SearchFilters())
    val filters: StateFlow<SearchFilters> = _filters.asStateFlow()
    
    init {
        // Observe search query changes and perform search with debounce
        searchQuery
            .debounce(500) // Wait 500ms after user stops typing
            .distinctUntilChanged()
            .filter { it.isNotBlank() && it.length >= 2 }
            .onEach { query ->
                searchAnime(query)
            }
            .launchIn(viewModelScope)
    }
    
    fun updateSearchQuery(query: String) {
        _searchQuery.value = query
        if (query.isBlank()) {
            _uiState.value = _uiState.value.copy(
                searchResults = emptyList(),
                isLoading = false,
                error = null
            )
        }
    }
    
    fun searchAnime(query: String = _searchQuery.value, page: Int = 1) {
        if (query.isBlank() || query.length < 2) return
        
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(
                isLoading = true,
                error = null
            )
            
            try {
                animeRepository.searchAnime(query, page)
                    .onSuccess { response ->
                        val currentResults = if (page == 1) emptyList() else _uiState.value.searchResults
                        _uiState.value = _uiState.value.copy(
                            searchResults = currentResults + response.data,
                            isLoading = false,
                            hasNextPage = response.pagination?.hasNextPage ?: false,
                            currentPage = page
                        )
                    }
                    .onFailure { error ->
                        _uiState.value = _uiState.value.copy(
                            isLoading = false,
                            error = error.message ?: "Search failed"
                        )
                    }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = e.message ?: "Unknown error occurred"
                )
            }
        }
    }
    
    fun loadNextPage() {
        if (_uiState.value.isLoading || !_uiState.value.hasNextPage) return
        
        val nextPage = _uiState.value.currentPage + 1
        searchAnime(_searchQuery.value, nextPage)
    }
    
    fun searchWithFilters() {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(
                isLoading = true,
                error = null,
                searchResults = emptyList()
            )
            
            val currentFilters = _filters.value
            val query = _searchQuery.value
            
            try {
                when {
                    currentFilters.genreId != null -> {
                        animeRepository.getAnimeByGenre(
                            genreId = currentFilters.genreId,
                            page = 1,
                            orderBy = currentFilters.sortBy,
                            sort = currentFilters.sortOrder
                        )
                    }
                    currentFilters.year != null -> {
                        animeRepository.getAnimeByYear(
                            startDate = "${currentFilters.year}-01-01",
                            endDate = "${currentFilters.year}-12-31",
                            page = 1
                        )
                    }
                    currentFilters.status != null -> {
                        animeRepository.getAnimeByStatus(
                            status = currentFilters.status,
                            page = 1
                        )
                    }
                    else -> {
                        animeRepository.searchAnime(
                            query = query,
                            page = 1,
                            orderBy = currentFilters.sortBy,
                            sort = currentFilters.sortOrder
                        )
                    }
                }.onSuccess { response ->
                    _uiState.value = _uiState.value.copy(
                        searchResults = response.data,
                        isLoading = false,
                        hasNextPage = response.pagination?.hasNextPage ?: false,
                        currentPage = 1
                    )
                }.onFailure { error ->
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        error = error.message ?: "Search failed"
                    )
                }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = e.message ?: "Unknown error occurred"
                )
            }
        }
    }
    
    fun updateFilters(filters: SearchFilters) {
        _filters.value = filters
        if (_searchQuery.value.isNotBlank()) {
            searchWithFilters()
        }
    }
    
    fun clearFilters() {
        _filters.value = SearchFilters()
        if (_searchQuery.value.isNotBlank()) {
            searchAnime(_searchQuery.value, 1)
        }
    }
    
    fun clearError() {
        _uiState.value = _uiState.value.copy(error = null)
    }
    
    fun clearResults() {
        _uiState.value = _uiState.value.copy(
            searchResults = emptyList(),
            currentPage = 1,
            hasNextPage = false
        )
        _searchQuery.value = ""
    }
}

data class SearchUiState(
    val isLoading: Boolean = false,
    val searchResults: List<Anime> = emptyList(),
    val error: String? = null,
    val hasNextPage: Boolean = false,
    val currentPage: Int = 1
)

data class SearchFilters(
    val genreId: Int? = null,
    val year: Int? = null,
    val status: String? = null, // airing, complete, upcoming
    val sortBy: String = "score", // score, popularity, title, start_date
    val sortOrder: String = "desc", // asc, desc
    val minScore: Double? = null,
    val maxScore: Double? = null
)
