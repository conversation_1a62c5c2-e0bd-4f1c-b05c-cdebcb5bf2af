package com.animeplus.app.data.repository

import com.animeplus.app.data.api.JikanApiService
import com.animeplus.app.data.api.StreamingApiService
import com.animeplus.app.data.database.AnimeDao
import com.animeplus.app.data.model.*
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.first
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class AnimeRepository @Inject constructor(
    private val jikanApi: JikanApiService,
    private val streamingApi: StreamingApiService,
    private val animeDao: AnimeDao
) {
    
    // Network + Cache operations
    suspend fun searchAnime(
        query: String,
        page: Int = 1,
        forceRefresh: Boolean = false
    ): Result<AnimeResponse> {
        return try {
            if (!forceRefresh) {
                // Try to get from cache first
                val cachedResults = animeDao.searchAnime(query).first()
                if (cachedResults.isNotEmpty()) {
                    return Result.success(
                        AnimeResponse(
                            data = cachedResults,
                            pagination = null
                        )
                    )
                }
            }
            
            val response = jikanApi.searchAnime(query, page)
            if (response.isSuccessful && response.body() != null) {
                val animeResponse = response.body()!!
                // Cache the results
                animeDao.insertAnimeList(animeResponse.data)
                Result.success(animeResponse)
            } else {
                Result.failure(Exception("Failed to search anime: ${response.message()}"))
            }
        } catch (e: Exception) {
            // Fallback to cache
            val cachedResults = animeDao.searchAnime(query).first()
            if (cachedResults.isNotEmpty()) {
                Result.success(
                    AnimeResponse(
                        data = cachedResults,
                        pagination = null
                    )
                )
            } else {
                Result.failure(e)
            }
        }
    }
    
    suspend fun getTopAnime(
        type: String = "tv",
        filter: String = "bypopularity",
        page: Int = 1,
        forceRefresh: Boolean = false
    ): Result<AnimeResponse> {
        return try {
            val response = jikanApi.getTopAnime(type, filter, page)
            if (response.isSuccessful && response.body() != null) {
                val animeResponse = response.body()!!
                // Cache the results
                animeDao.insertAnimeList(animeResponse.data)
                Result.success(animeResponse)
            } else {
                Result.failure(Exception("Failed to get top anime: ${response.message()}"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    suspend fun getAnimeDetails(animeId: Int): Result<Anime> {
        return try {
            // Try cache first
            val cachedAnime = animeDao.getAnimeById(animeId)
            if (cachedAnime != null) {
                return Result.success(cachedAnime)
            }
            
            val response = jikanApi.getAnimeDetails(animeId)
            if (response.isSuccessful && response.body() != null) {
                val anime = response.body()!!.data
                // Cache the result
                animeDao.insertAnime(anime)
                Result.success(anime)
            } else {
                Result.failure(Exception("Failed to get anime details: ${response.message()}"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    suspend fun getAnimeEpisodes(animeId: Int, page: Int = 1): Result<EpisodeResponse> {
        return try {
            val response = jikanApi.getAnimeEpisodes(animeId, page)
            if (response.isSuccessful && response.body() != null) {
                val episodeResponse = response.body()!!
                // Cache episodes
                val episodesWithAnimeId = episodeResponse.data.map { episode ->
                    episode.copy(animeId = animeId)
                }
                animeDao.insertEpisodeList(episodesWithAnimeId)
                Result.success(episodeResponse)
            } else {
                Result.failure(Exception("Failed to get episodes: ${response.message()}"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    suspend fun getCurrentSeasonAnime(page: Int = 1): Result<AnimeResponse> {
        return try {
            val response = jikanApi.getCurrentSeasonAnime(page)
            if (response.isSuccessful && response.body() != null) {
                val animeResponse = response.body()!!
                animeDao.insertAnimeList(animeResponse.data)
                Result.success(animeResponse)
            } else {
                Result.failure(Exception("Failed to get current season anime: ${response.message()}"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    suspend fun getUpcomingSeasonAnime(page: Int = 1): Result<AnimeResponse> {
        return try {
            val response = jikanApi.getUpcomingSeasonAnime(page)
            if (response.isSuccessful && response.body() != null) {
                val animeResponse = response.body()!!
                animeDao.insertAnimeList(animeResponse.data)
                Result.success(animeResponse)
            } else {
                Result.failure(Exception("Failed to get upcoming anime: ${response.message()}"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    // Streaming operations
    suspend fun searchStreamingAnime(keyword: String, page: Int = 1): Result<StreamingSearchResponse> {
        return try {
            val response = streamingApi.searchAnime(keyword, page)
            if (response.isSuccessful && response.body() != null) {
                Result.success(response.body()!!)
            } else {
                Result.failure(Exception("Failed to search streaming anime: ${response.message()}"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    suspend fun getStreamingAnimeInfo(animeId: String): Result<StreamingAnime> {
        return try {
            val response = streamingApi.getAnimeInfo(animeId)
            if (response.isSuccessful && response.body() != null) {
                Result.success(response.body()!!)
            } else {
                Result.failure(Exception("Failed to get streaming anime info: ${response.message()}"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    suspend fun getEpisodeStreamingData(episodeId: String): Result<StreamingData> {
        return try {
            val response = streamingApi.getEpisodeStreamingData(episodeId)
            if (response.isSuccessful && response.body() != null) {
                Result.success(response.body()!!)
            } else {
                Result.failure(Exception("Failed to get streaming data: ${response.message()}"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    suspend fun getRecentEpisodes(page: Int = 1, type: Int = 1): Result<RecentEpisodesResponse> {
        return try {
            val response = streamingApi.getRecentEpisodes(page, type)
            if (response.isSuccessful && response.body() != null) {
                Result.success(response.body()!!)
            } else {
                Result.failure(Exception("Failed to get recent episodes: ${response.message()}"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    suspend fun getPopularStreamingAnime(page: Int = 1): Result<StreamingSearchResponse> {
        return try {
            val response = streamingApi.getPopularAnime(page)
            if (response.isSuccessful && response.body() != null) {
                Result.success(response.body()!!)
            } else {
                Result.failure(Exception("Failed to get popular anime: ${response.message()}"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    // Local database operations
    fun getAllAnime(): Flow<List<Anime>> = animeDao.getAllAnime()
    
    fun searchAnimeLocal(query: String): Flow<List<Anime>> = animeDao.searchAnime(query)
    
    suspend fun getAnimeByIdLocal(animeId: Int): Anime? = animeDao.getAnimeById(animeId)
    
    fun getEpisodesByAnime(animeId: Int): Flow<List<Episode>> = animeDao.getEpisodesByAnime(animeId)
    
    suspend fun insertAnime(anime: Anime) = animeDao.insertAnime(anime)
    
    suspend fun updateAnime(anime: Anime) = animeDao.updateAnime(anime)
    
    suspend fun deleteAnime(anime: Anime) = animeDao.deleteAnime(anime)
    
    // Cache management
    suspend fun clearCache() {
        animeDao.clearAllAnime()
    }
    
    suspend fun clearNonFavoriteCache() {
        animeDao.clearNonFavoriteAnime()
    }
    
    fun getRecentlyWatchedAnime(limit: Int = 10): Flow<List<Anime>> = 
        animeDao.getRecentlyWatchedAnime(limit)
}
