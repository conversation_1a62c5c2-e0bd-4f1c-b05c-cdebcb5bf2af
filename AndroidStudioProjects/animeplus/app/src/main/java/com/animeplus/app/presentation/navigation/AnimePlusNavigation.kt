package com.animeplus.app.presentation.navigation

import androidx.compose.foundation.layout.padding
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Favorite
import androidx.compose.material.icons.filled.Home
import androidx.compose.material.icons.filled.Person
import androidx.compose.material.icons.filled.Search
import androidx.compose.material.icons.outlined.FavoriteBorder
import androidx.compose.material.icons.outlined.Home
import androidx.compose.material.icons.outlined.Person
import androidx.compose.material.icons.outlined.Search
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.navigation.NavDestination.Companion.hierarchy
import androidx.navigation.NavGraph.Companion.findStartDestination
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.currentBackStackEntryAsState
import androidx.navigation.compose.rememberNavController
import com.animeplus.app.presentation.screens.auth.AuthScreen
import com.animeplus.app.presentation.screens.detail.AnimeDetailScreen
import com.animeplus.app.presentation.screens.favorites.FavoritesScreen
import com.animeplus.app.presentation.screens.home.HomeScreen
import com.animeplus.app.presentation.screens.player.VideoPlayerScreen
import com.animeplus.app.presentation.screens.profile.ProfileScreen
import com.animeplus.app.presentation.screens.search.SearchScreen

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AnimePlusNavigation() {
    val navController = rememberNavController()
    
    Scaffold(
        bottomBar = {
            BottomNavigationBar(navController = navController)
        }
    ) { innerPadding ->
        NavHost(
            navController = navController,
            startDestination = Screen.Home.route,
            modifier = Modifier.padding(innerPadding)
        ) {
            composable(Screen.Home.route) {
                HomeScreen(
                    onAnimeClick = { animeId ->
                        navController.navigate("${Screen.AnimeDetail.route}/$animeId")
                    }
                )
            }
            
            composable(Screen.Search.route) {
                SearchScreen(
                    onAnimeClick = { animeId ->
                        navController.navigate("${Screen.AnimeDetail.route}/$animeId")
                    }
                )
            }
            
            composable(Screen.Favorites.route) {
                FavoritesScreen(
                    onAnimeClick = { animeId ->
                        navController.navigate("${Screen.AnimeDetail.route}/$animeId")
                    }
                )
            }
            
            composable(Screen.Profile.route) {
                ProfileScreen(
                    onLoginClick = {
                        navController.navigate(Screen.Auth.route)
                    }
                )
            }
            
            composable("${Screen.AnimeDetail.route}/{animeId}") { backStackEntry ->
                val animeId = backStackEntry.arguments?.getString("animeId")?.toIntOrNull() ?: 0
                AnimeDetailScreen(
                    animeId = animeId,
                    onBackClick = {
                        navController.popBackStack()
                    },
                    onPlayEpisode = { episodeId ->
                        navController.navigate("${Screen.VideoPlayer.route}/$episodeId")
                    }
                )
            }
            
            composable("${Screen.VideoPlayer.route}/{episodeId}") { backStackEntry ->
                val episodeId = backStackEntry.arguments?.getString("episodeId") ?: ""
                VideoPlayerScreen(
                    episodeId = episodeId,
                    onBackClick = {
                        navController.popBackStack()
                    }
                )
            }
            
            composable(Screen.Auth.route) {
                AuthScreen(
                    onAuthSuccess = {
                        navController.popBackStack()
                    },
                    onBackClick = {
                        navController.popBackStack()
                    }
                )
            }
        }
    }
}

@Composable
fun BottomNavigationBar(navController: NavHostController) {
    val navBackStackEntry by navController.currentBackStackEntryAsState()
    val currentDestination = navBackStackEntry?.destination
    
    // Only show bottom navigation for main screens
    val showBottomBar = currentDestination?.route in listOf(
        Screen.Home.route,
        Screen.Search.route,
        Screen.Favorites.route,
        Screen.Profile.route
    )
    
    if (showBottomBar) {
        NavigationBar {
            bottomNavItems.forEach { screen ->
                NavigationBarItem(
                    icon = {
                        Icon(
                            imageVector = if (currentDestination?.hierarchy?.any { it.route == screen.route } == true) {
                                screen.selectedIcon
                            } else {
                                screen.unselectedIcon
                            },
                            contentDescription = screen.title
                        )
                    },
                    label = { Text(screen.title) },
                    selected = currentDestination?.hierarchy?.any { it.route == screen.route } == true,
                    onClick = {
                        navController.navigate(screen.route) {
                            // Pop up to the start destination of the graph to
                            // avoid building up a large stack of destinations
                            // on the back stack as users select items
                            popUpTo(navController.graph.findStartDestination().id) {
                                saveState = true
                            }
                            // Avoid multiple copies of the same destination when
                            // reselecting the same item
                            launchSingleTop = true
                            // Restore state when reselecting a previously selected item
                            restoreState = true
                        }
                    }
                )
            }
        }
    }
}

sealed class Screen(
    val route: String,
    val title: String,
    val selectedIcon: ImageVector,
    val unselectedIcon: ImageVector
) {
    object Home : Screen(
        route = "home",
        title = "Home",
        selectedIcon = Icons.Filled.Home,
        unselectedIcon = Icons.Outlined.Home
    )
    
    object Search : Screen(
        route = "search",
        title = "Search",
        selectedIcon = Icons.Filled.Search,
        unselectedIcon = Icons.Outlined.Search
    )
    
    object Favorites : Screen(
        route = "favorites",
        title = "Favorites",
        selectedIcon = Icons.Filled.Favorite,
        unselectedIcon = Icons.Outlined.FavoriteBorder
    )
    
    object Profile : Screen(
        route = "profile",
        title = "Profile",
        selectedIcon = Icons.Filled.Person,
        unselectedIcon = Icons.Outlined.Person
    )
    
    object AnimeDetail : Screen(
        route = "anime_detail",
        title = "Anime Detail",
        selectedIcon = Icons.Filled.Home,
        unselectedIcon = Icons.Outlined.Home
    )
    
    object VideoPlayer : Screen(
        route = "video_player",
        title = "Video Player",
        selectedIcon = Icons.Filled.Home,
        unselectedIcon = Icons.Outlined.Home
    )
    
    object Auth : Screen(
        route = "auth",
        title = "Authentication",
        selectedIcon = Icons.Filled.Person,
        unselectedIcon = Icons.Outlined.Person
    )
}

val bottomNavItems = listOf(
    Screen.Home,
    Screen.Search,
    Screen.Favorites,
    Screen.Profile
)
