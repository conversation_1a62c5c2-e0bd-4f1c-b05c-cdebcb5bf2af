package com.animeplus.app.data.model

import androidx.room.Entity
import androidx.room.PrimaryKey
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
@Entity(tableName = "episodes")
data class Episode(
    @PrimaryKey
    @SerialName("mal_id")
    val malId: Int,
    
    @SerialName("title")
    val title: String? = null,
    
    @SerialName("title_japanese")
    val titleJapanese: String? = null,
    
    @SerialName("title_romanji")
    val titleRomanji: String? = null,
    
    @SerialName("aired")
    val aired: String? = null,
    
    @SerialName("score")
    val score: Double? = null,
    
    @SerialName("filler")
    val filler: Boolean = false,
    
    @SerialName("recap")
    val recap: Boolean = false,
    
    @SerialName("forum_url")
    val forumUrl: String? = null,
    
    // For streaming
    val animeId: Int,
    val episodeNumber: Int,
    val streamingUrl: String? = null,
    val downloadUrl: String? = null,
    val subtitleUrl: String? = null,
    
    // Local tracking
    val watchProgress: Long = 0L, // in milliseconds
    val duration: Long = 0L, // in milliseconds
    val isWatched: Boolean = false,
    val lastWatchedAt: Long? = null
)

@Serializable
data class EpisodeResponse(
    @SerialName("data")
    val data: List<Episode>
)

@Serializable
data class StreamingEpisode(
    @SerialName("id")
    val id: String,
    
    @SerialName("title")
    val title: String,
    
    @SerialName("url")
    val url: String,
    
    @SerialName("number")
    val number: Int
)

@Serializable
data class StreamingAnime(
    @SerialName("id")
    val id: String,
    
    @SerialName("title")
    val title: String,
    
    @SerialName("url")
    val url: String,
    
    @SerialName("image")
    val image: String? = null,
    
    @SerialName("releaseDate")
    val releaseDate: String? = null,
    
    @SerialName("description")
    val description: String? = null,
    
    @SerialName("genres")
    val genres: List<String> = emptyList(),
    
    @SerialName("totalEpisodes")
    val totalEpisodes: Int? = null,
    
    @SerialName("episodes")
    val episodes: List<StreamingEpisode> = emptyList(),
    
    @SerialName("type")
    val type: String? = null,
    
    @SerialName("status")
    val status: String? = null,
    
    @SerialName("otherName")
    val otherName: String? = null,
    
    @SerialName("subOrDub")
    val subOrDub: String? = null
)

@Serializable
data class StreamingSearchResponse(
    @SerialName("results")
    val results: List<StreamingAnime> = emptyList(),
    
    @SerialName("hasNextPage")
    val hasNextPage: Boolean = false,
    
    @SerialName("currentPage")
    val currentPage: Int = 1
)

@Serializable
data class StreamingSource(
    @SerialName("url")
    val url: String,
    
    @SerialName("isM3U8")
    val isM3U8: Boolean = false,
    
    @SerialName("quality")
    val quality: String? = null
)

@Serializable
data class StreamingData(
    @SerialName("headers")
    val headers: Map<String, String> = emptyMap(),
    
    @SerialName("sources")
    val sources: List<StreamingSource> = emptyList(),
    
    @SerialName("download")
    val download: String? = null,
    
    @SerialName("subtitle")
    val subtitle: String? = null
)

// Local database entities
@Entity(tableName = "watch_history")
data class WatchHistory(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    val animeId: Int,
    val episodeId: Int,
    val episodeNumber: Int,
    val animeTitle: String,
    val episodeTitle: String? = null,
    val thumbnailUrl: String? = null,
    val watchProgress: Long = 0L,
    val duration: Long = 0L,
    val lastWatchedAt: Long = System.currentTimeMillis()
)

@Entity(tableName = "favorites")
data class FavoriteAnime(
    @PrimaryKey
    val animeId: Int,
    val title: String,
    val imageUrl: String? = null,
    val score: Double? = null,
    val episodes: Int? = null,
    val status: String? = null,
    val addedAt: Long = System.currentTimeMillis()
)
