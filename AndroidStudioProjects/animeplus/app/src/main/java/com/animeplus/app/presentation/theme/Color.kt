package com.animeplus.app.presentation.theme

import androidx.compose.ui.graphics.Color

// Primary Colors
val PrimaryRed = Color(0xFFE50914)
val PrimaryRedDark = Color(0xFFB81D24)
val PrimaryRedLight = Color(0xFFFF4757)

// Secondary Colors
val SecondaryBlue = Color(0xFF00D4FF)
val SecondaryBlueDark = Color(0xFF0099CC)
val SecondaryBlueLight = Color(0xFF33E0FF)

// Background Colors
val BackgroundDark = Color(0xFF000000)
val BackgroundDarkSecondary = Color(0xFF1A1A1A)
val BackgroundDarkTertiary = Color(0xFF2A2A2A)

val BackgroundLight = Color(0xFFFFFFFF)
val BackgroundLightSecondary = Color(0xFFF5F5F5)
val BackgroundLightTertiary = Color(0xFFE0E0E0)

// Surface Colors
val SurfaceDark = Color(0xFF1E1E1E)
val SurfaceDarkSecondary = Color(0xFF2E2E2E)

val SurfaceLight = Color(0xFFFFFFFF)
val SurfaceLightSecondary = Color(0xFFF8F8F8)

// Text Colors
val TextPrimary = Color(0xFFFFFFFF)
val TextSecondary = Color(0xFFB3B3B3)
val TextTertiary = Color(0xFF666666)

val TextPrimaryLight = Color(0xFF000000)
val TextSecondaryLight = Color(0xFF666666)
val TextTertiaryLight = Color(0xFF999999)

// Accent Colors
val AccentGreen = Color(0xFF4CAF50)
val AccentOrange = Color(0xFFFF9800)
val AccentPurple = Color(0xFF9C27B0)
val AccentYellow = Color(0xFFFFC107)

// Status Colors
val SuccessGreen = Color(0xFF4CAF50)
val WarningOrange = Color(0xFFFF9800)
val ErrorRed = Color(0xFFF44336)
val InfoBlue = Color(0xFF2196F3)

// Rating Colors
val RatingExcellent = Color(0xFF4CAF50)  // 8.5+
val RatingGood = Color(0xFF8BC34A)       // 7.0-8.4
val RatingAverage = Color(0xFFFFC107)    // 5.5-6.9
val RatingPoor = Color(0xFFFF9800)       // 4.0-5.4
val RatingBad = Color(0xFFF44336)        // <4.0

// Genre Colors
val GenreAction = Color(0xFFE91E63)
val GenreAdventure = Color(0xFF4CAF50)
val GenreComedy = Color(0xFFFFC107)
val GenreDrama = Color(0xFF9C27B0)
val GenreFantasy = Color(0xFF673AB7)
val GenreRomance = Color(0xFFE91E63)
val GenreSciFi = Color(0xFF00BCD4)
val GenreThriller = Color(0xFF795548)

// Gradient Colors
val GradientStart = Color(0xFF000000)
val GradientEnd = Color(0x00000000)

// Card Colors
val CardDark = Color(0xFF1E1E1E)
val CardLight = Color(0xFFFFFFFF)

// Border Colors
val BorderDark = Color(0xFF333333)
val BorderLight = Color(0xFFE0E0E0)

// Shimmer Colors
val ShimmerColorsDark = listOf(
    Color(0xFF1E1E1E),
    Color(0xFF2E2E2E),
    Color(0xFF1E1E1E)
)

val ShimmerColorsLight = listOf(
    Color(0xFFE0E0E0),
    Color(0xFFF0F0F0),
    Color(0xFFE0E0E0)
)
