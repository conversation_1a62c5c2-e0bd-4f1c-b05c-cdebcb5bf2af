package com.animeplus.app.data.api

import com.animeplus.app.data.model.*
import retrofit2.Response
import retrofit2.http.GET
import retrofit2.http.Path
import retrofit2.http.Query

interface JikanApiService {
    
    @GET("anime")
    suspend fun searchAnime(
        @Query("q") query: String,
        @Query("page") page: Int = 1,
        @Query("limit") limit: Int = 25,
        @Query("order_by") orderBy: String = "score",
        @Query("sort") sort: String = "desc"
    ): Response<AnimeResponse>
    
    @GET("top/anime")
    suspend fun getTopAnime(
        @Query("type") type: String = "tv",
        @Query("filter") filter: String = "bypopularity",
        @Query("page") page: Int = 1,
        @Query("limit") limit: Int = 25
    ): Response<AnimeResponse>
    
    @GET("anime/{id}/full")
    suspend fun getAnimeDetails(
        @Path("id") animeId: Int
    ): Response<AnimeDetailResponse>
    
    @GET("anime/{id}/episodes")
    suspend fun getAnimeEpisodes(
        @Path("id") animeId: Int,
        @Query("page") page: Int = 1
    ): Response<EpisodeResponse>
    
    @GET("seasons/now")
    suspend fun getCurrentSeasonAnime(
        @Query("page") page: Int = 1,
        @Query("limit") limit: Int = 25
    ): Response<AnimeResponse>
    
    @GET("seasons/upcoming")
    suspend fun getUpcomingSeasonAnime(
        @Query("page") page: Int = 1,
        @Query("limit") limit: Int = 25
    ): Response<AnimeResponse>
    
    @GET("anime")
    suspend fun getAnimeByGenre(
        @Query("genres") genreId: Int,
        @Query("page") page: Int = 1,
        @Query("limit") limit: Int = 25,
        @Query("order_by") orderBy: String = "score",
        @Query("sort") sort: String = "desc"
    ): Response<AnimeResponse>
    
    @GET("anime")
    suspend fun getAnimeByYear(
        @Query("start_date") startDate: String,
        @Query("end_date") endDate: String,
        @Query("page") page: Int = 1,
        @Query("limit") limit: Int = 25
    ): Response<AnimeResponse>
    
    @GET("anime")
    suspend fun getAnimeByStatus(
        @Query("status") status: String, // airing, complete, upcoming
        @Query("page") page: Int = 1,
        @Query("limit") limit: Int = 25
    ): Response<AnimeResponse>
    
    @GET("genres/anime")
    suspend fun getAnimeGenres(): Response<GenreResponse>
    
    @GET("anime/{id}/characters")
    suspend fun getAnimeCharacters(
        @Path("id") animeId: Int
    ): Response<CharacterResponse>
    
    @GET("anime/{id}/staff")
    suspend fun getAnimeStaff(
        @Path("id") animeId: Int
    ): Response<StaffResponse>
    
    @GET("anime/{id}/recommendations")
    suspend fun getAnimeRecommendations(
        @Path("id") animeId: Int
    ): Response<RecommendationResponse>
}

// Additional response models for extended features
@kotlinx.serialization.Serializable
data class GenreResponse(
    @kotlinx.serialization.SerialName("data")
    val data: List<AnimeGenre>
)

@kotlinx.serialization.Serializable
data class Character(
    @kotlinx.serialization.SerialName("character")
    val character: CharacterInfo,
    
    @kotlinx.serialization.SerialName("role")
    val role: String,
    
    @kotlinx.serialization.SerialName("voice_actors")
    val voiceActors: List<VoiceActor> = emptyList()
)

@kotlinx.serialization.Serializable
data class CharacterInfo(
    @kotlinx.serialization.SerialName("mal_id")
    val malId: Int,
    
    @kotlinx.serialization.SerialName("name")
    val name: String,
    
    @kotlinx.serialization.SerialName("images")
    val images: CharacterImages? = null
)

@kotlinx.serialization.Serializable
data class CharacterImages(
    @kotlinx.serialization.SerialName("jpg")
    val jpg: CharacterImageJpg? = null
)

@kotlinx.serialization.Serializable
data class CharacterImageJpg(
    @kotlinx.serialization.SerialName("image_url")
    val imageUrl: String? = null
)

@kotlinx.serialization.Serializable
data class VoiceActor(
    @kotlinx.serialization.SerialName("person")
    val person: PersonInfo,
    
    @kotlinx.serialization.SerialName("language")
    val language: String
)

@kotlinx.serialization.Serializable
data class PersonInfo(
    @kotlinx.serialization.SerialName("mal_id")
    val malId: Int,
    
    @kotlinx.serialization.SerialName("name")
    val name: String,
    
    @kotlinx.serialization.SerialName("images")
    val images: PersonImages? = null
)

@kotlinx.serialization.Serializable
data class PersonImages(
    @kotlinx.serialization.SerialName("jpg")
    val jpg: PersonImageJpg? = null
)

@kotlinx.serialization.Serializable
data class PersonImageJpg(
    @kotlinx.serialization.SerialName("image_url")
    val imageUrl: String? = null
)

@kotlinx.serialization.Serializable
data class CharacterResponse(
    @kotlinx.serialization.SerialName("data")
    val data: List<Character>
)

@kotlinx.serialization.Serializable
data class StaffResponse(
    @kotlinx.serialization.SerialName("data")
    val data: List<Staff>
)

@kotlinx.serialization.Serializable
data class Staff(
    @kotlinx.serialization.SerialName("person")
    val person: PersonInfo,
    
    @kotlinx.serialization.SerialName("positions")
    val positions: List<String>
)

@kotlinx.serialization.Serializable
data class RecommendationResponse(
    @kotlinx.serialization.SerialName("data")
    val data: List<Recommendation>
)

@kotlinx.serialization.Serializable
data class Recommendation(
    @kotlinx.serialization.SerialName("entry")
    val entry: Anime,
    
    @kotlinx.serialization.SerialName("votes")
    val votes: Int
)
