package com.animeplus.app.presentation.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.animeplus.app.data.model.*
import com.animeplus.app.data.repository.AnimeRepository
import com.animeplus.app.data.repository.UserRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class AnimeDetailViewModel @Inject constructor(
    private val animeRepository: AnimeRepository,
    private val userRepository: UserRepository
) : ViewModel() {
    
    private val _uiState = MutableStateFlow(AnimeDetailUiState())
    val uiState: StateFlow<AnimeDetailUiState> = _uiState.asStateFlow()
    
    private var currentAnimeId: Int = 0
    
    fun loadAnimeDetails(animeId: Int) {
        currentAnimeId = animeId
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true, error = null)
            
            try {
                // Load anime details
                animeRepository.getAnimeDetails(animeId)
                    .onSuccess { anime ->
                        _uiState.value = _uiState.value.copy(
                            anime = anime,
                            isLoading = false
                        )
                        
                        // Check if anime is in favorites
                        checkFavoriteStatus(animeId)
                        
                        // Load additional data
                        loadEpisodes(animeId)
                        loadStreamingInfo(anime.title)
                        loadWatchHistory(animeId)
                    }
                    .onFailure { error ->
                        _uiState.value = _uiState.value.copy(
                            isLoading = false,
                            error = error.message ?: "Failed to load anime details"
                        )
                    }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = e.message ?: "Unknown error occurred"
                )
            }
        }
    }
    
    private suspend fun loadEpisodes(animeId: Int) {
        animeRepository.getAnimeEpisodes(animeId, 1)
            .onSuccess { response ->
                _uiState.value = _uiState.value.copy(
                    episodes = response.data
                )
            }
            .onFailure { error ->
                // Handle error silently for episodes
            }
    }
    
    private suspend fun loadStreamingInfo(animeTitle: String) {
        animeRepository.searchStreamingAnime(animeTitle, 1)
            .onSuccess { response ->
                val streamingAnime = response.results.firstOrNull { streamingAnime ->
                    streamingAnime.title.contains(animeTitle, ignoreCase = true) ||
                    animeTitle.contains(streamingAnime.title, ignoreCase = true)
                }
                
                streamingAnime?.let { anime ->
                    animeRepository.getStreamingAnimeInfo(anime.id)
                        .onSuccess { detailedStreamingAnime ->
                            _uiState.value = _uiState.value.copy(
                                streamingAnime = detailedStreamingAnime
                            )
                        }
                }
            }
            .onFailure { error ->
                // Handle error silently for streaming info
            }
    }
    
    private suspend fun checkFavoriteStatus(animeId: Int) {
        val isFavorite = userRepository.isFavorite(animeId)
        _uiState.value = _uiState.value.copy(isFavorite = isFavorite)
    }
    
    private suspend fun loadWatchHistory(animeId: Int) {
        userRepository.getWatchHistoryByAnime(animeId)
            .collect { watchHistory ->
                _uiState.value = _uiState.value.copy(
                    watchHistory = watchHistory
                )
            }
    }
    
    fun toggleFavorite() {
        val anime = _uiState.value.anime ?: return
        
        viewModelScope.launch {
            if (_uiState.value.isFavorite) {
                userRepository.removeFromFavorites(anime.malId)
                    .onSuccess {
                        _uiState.value = _uiState.value.copy(isFavorite = false)
                    }
                    .onFailure { error ->
                        _uiState.value = _uiState.value.copy(
                            error = "Failed to remove from favorites: ${error.message}"
                        )
                    }
            } else {
                userRepository.addToFavorites(anime)
                    .onSuccess {
                        _uiState.value = _uiState.value.copy(isFavorite = true)
                    }
                    .onFailure { error ->
                        _uiState.value = _uiState.value.copy(
                            error = "Failed to add to favorites: ${error.message}"
                        )
                    }
            }
        }
    }
    
    fun getEpisodeStreamingData(episodeId: String, callback: (Result<StreamingData>) -> Unit) {
        viewModelScope.launch {
            animeRepository.getEpisodeStreamingData(episodeId)
                .onSuccess { streamingData ->
                    callback(Result.success(streamingData))
                }
                .onFailure { error ->
                    callback(Result.failure(error))
                }
        }
    }
    
    fun markEpisodeAsWatched(
        episodeId: Int,
        episodeNumber: Int,
        episodeTitle: String?,
        watchProgress: Long = 0L,
        duration: Long = 0L
    ) {
        val anime = _uiState.value.anime ?: return
        
        viewModelScope.launch {
            userRepository.addToWatchHistory(
                animeId = anime.malId,
                episodeId = episodeId,
                episodeNumber = episodeNumber,
                animeTitle = anime.title,
                episodeTitle = episodeTitle,
                thumbnailUrl = anime.images?.jpg?.imageUrl,
                watchProgress = watchProgress,
                duration = duration
            )
        }
    }
    
    fun updateWatchProgress(
        episodeId: Int,
        watchProgress: Long,
        duration: Long
    ) {
        val anime = _uiState.value.anime ?: return
        
        viewModelScope.launch {
            userRepository.updateWatchProgress(
                animeId = anime.malId,
                episodeId = episodeId,
                watchProgress = watchProgress,
                duration = duration
            )
        }
    }
    
    fun clearError() {
        _uiState.value = _uiState.value.copy(error = null)
    }
}

data class AnimeDetailUiState(
    val isLoading: Boolean = false,
    val anime: Anime? = null,
    val episodes: List<Episode> = emptyList(),
    val streamingAnime: StreamingAnime? = null,
    val watchHistory: List<WatchHistory> = emptyList(),
    val isFavorite: Boolean = false,
    val error: String? = null
)
