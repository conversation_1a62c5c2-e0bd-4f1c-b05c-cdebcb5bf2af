package com.animeplus.app.presentation.screens.detail

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Favorite
import androidx.compose.material.icons.filled.FavoriteBorder
import androidx.compose.material.icons.filled.PlayArrow
import androidx.compose.material.icons.filled.Share
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import coil.compose.AsyncImage
import com.animeplus.app.data.model.Episode
import com.animeplus.app.presentation.components.*
import com.animeplus.app.presentation.viewmodel.AnimeDetailViewModel

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AnimeDetailScreen(
    animeId: Int,
    onBackClick: () -> Unit,
    onPlayEpisode: (String) -> Unit,
    viewModel: AnimeDetailViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsState()
    
    LaunchedEffect(animeId) {
        viewModel.loadAnimeDetails(animeId)
    }
    
    when {
        uiState.isLoading -> {
            FullScreenLoading("Loading anime details...")
        }
        
        uiState.error != null && uiState.anime == null -> {
            FullScreenError(
                title = "Failed to Load",
                message = uiState.error,
                onRetry = { viewModel.loadAnimeDetails(animeId) }
            )
        }
        
        uiState.anime != null -> {
            val anime = uiState.anime!!
            
            LazyColumn(
                modifier = Modifier.fillMaxSize()
            ) {
                item {
                    // Hero Section
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(400.dp)
                    ) {
                        // Background Image
                        AsyncImage(
                            model = anime.images?.jpg?.largeImageUrl ?: anime.images?.jpg?.imageUrl,
                            contentDescription = anime.title,
                            modifier = Modifier.fillMaxSize(),
                            contentScale = ContentScale.Crop
                        )
                        
                        // Gradient Overlay
                        Box(
                            modifier = Modifier
                                .fillMaxSize()
                                .background(
                                    Brush.verticalGradient(
                                        colors = listOf(
                                            Color.Transparent,
                                            Color.Black.copy(alpha = 0.8f)
                                        ),
                                        startY = 200f
                                    )
                                )
                        )
                        
                        // Top Bar
                        TopAppBar(
                            title = { },
                            navigationIcon = {
                                IconButton(onClick = onBackClick) {
                                    Icon(
                                        imageVector = Icons.Default.ArrowBack,
                                        contentDescription = "Back",
                                        tint = Color.White
                                    )
                                }
                            },
                            actions = {
                                IconButton(onClick = { viewModel.toggleFavorite() }) {
                                    Icon(
                                        imageVector = if (uiState.isFavorite) Icons.Default.Favorite else Icons.Default.FavoriteBorder,
                                        contentDescription = if (uiState.isFavorite) "Remove from favorites" else "Add to favorites",
                                        tint = if (uiState.isFavorite) MaterialTheme.colorScheme.primary else Color.White
                                    )
                                }
                                
                                IconButton(onClick = { /* Share */ }) {
                                    Icon(
                                        imageVector = Icons.Default.Share,
                                        contentDescription = "Share",
                                        tint = Color.White
                                    )
                                }
                            },
                            colors = TopAppBarDefaults.topAppBarColors(
                                containerColor = Color.Transparent
                            )
                        )
                        
                        // Content Overlay
                        Column(
                            modifier = Modifier
                                .align(Alignment.BottomStart)
                                .padding(20.dp)
                                .fillMaxWidth()
                        ) {
                            // Score and Status
                            Row(
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                anime.score?.let { score ->
                                    ScoreBadge(score = score)
                                    Spacer(modifier = Modifier.width(8.dp))
                                }
                                
                                anime.status?.let { status ->
                                    StatusChip(status = status)
                                }
                            }
                            
                            Spacer(modifier = Modifier.height(12.dp))
                            
                            // Title
                            Text(
                                text = anime.title,
                                style = MaterialTheme.typography.headlineMedium,
                                fontWeight = FontWeight.Bold,
                                color = Color.White,
                                maxLines = 2,
                                overflow = TextOverflow.Ellipsis
                            )
                            
                            // Alternative titles
                            anime.titleEnglish?.let { englishTitle ->
                                if (englishTitle != anime.title) {
                                    Spacer(modifier = Modifier.height(4.dp))
                                    Text(
                                        text = englishTitle,
                                        style = MaterialTheme.typography.bodyLarge,
                                        color = Color.White.copy(alpha = 0.8f)
                                    )
                                }
                            }
                            
                            Spacer(modifier = Modifier.height(12.dp))
                            
                            // Metadata
                            Row(
                                horizontalArrangement = Arrangement.spacedBy(16.dp)
                            ) {
                                anime.year?.let { year ->
                                    Text(
                                        text = year.toString(),
                                        style = MaterialTheme.typography.bodyMedium,
                                        color = Color.White.copy(alpha = 0.8f)
                                    )
                                }
                                
                                anime.episodes?.let { episodes ->
                                    Text(
                                        text = "$episodes episodes",
                                        style = MaterialTheme.typography.bodyMedium,
                                        color = Color.White.copy(alpha = 0.8f)
                                    )
                                }
                                
                                anime.duration?.let { duration ->
                                    Text(
                                        text = duration,
                                        style = MaterialTheme.typography.bodyMedium,
                                        color = Color.White.copy(alpha = 0.8f)
                                    )
                                }
                            }
                            
                            Spacer(modifier = Modifier.height(16.dp))
                            
                            // Action Buttons
                            Row(
                                horizontalArrangement = Arrangement.spacedBy(12.dp)
                            ) {
                                Button(
                                    onClick = {
                                        // Play first episode or continue watching
                                        uiState.episodes.firstOrNull()?.let { episode ->
                                            onPlayEpisode(episode.malId.toString())
                                        }
                                    },
                                    colors = ButtonDefaults.buttonColors(
                                        containerColor = MaterialTheme.colorScheme.primary
                                    ),
                                    modifier = Modifier.weight(1f)
                                ) {
                                    Icon(
                                        imageVector = Icons.Default.PlayArrow,
                                        contentDescription = null,
                                        modifier = Modifier.size(18.dp)
                                    )
                                    Spacer(modifier = Modifier.width(8.dp))
                                    Text("Watch Now")
                                }
                                
                                OutlinedButton(
                                    onClick = { viewModel.toggleFavorite() },
                                    colors = ButtonDefaults.outlinedButtonColors(
                                        contentColor = Color.White
                                    ),
                                    border = ButtonDefaults.outlinedButtonBorder.copy(
                                        brush = Brush.linearGradient(listOf(Color.White, Color.White))
                                    )
                                ) {
                                    Icon(
                                        imageVector = if (uiState.isFavorite) Icons.Default.Favorite else Icons.Default.FavoriteBorder,
                                        contentDescription = null,
                                        modifier = Modifier.size(18.dp)
                                    )
                                    Spacer(modifier = Modifier.width(8.dp))
                                    Text(if (uiState.isFavorite) "Favorited" else "Add to List")
                                }
                            }
                        }
                    }
                }
                
                item {
                    Spacer(modifier = Modifier.height(20.dp))
                }
                
                // Synopsis Section
                anime.synopsis?.let { synopsis ->
                    item {
                        DetailSection(
                            title = "Synopsis",
                            modifier = Modifier.padding(horizontal = 16.dp)
                        ) {
                            Text(
                                text = synopsis,
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurface
                            )
                        }
                    }
                }
                
                // Genres Section
                if (anime.genres.isNotEmpty()) {
                    item {
                        DetailSection(
                            title = "Genres",
                            modifier = Modifier.padding(horizontal = 16.dp)
                        ) {
                            LazyRow(
                                horizontalArrangement = Arrangement.spacedBy(8.dp)
                            ) {
                                items(anime.genres) { genre ->
                                    AssistChip(
                                        onClick = { /* Navigate to genre */ },
                                        label = { Text(genre.name) }
                                    )
                                }
                            }
                        }
                    }
                }
                
                // Episodes Section
                if (uiState.episodes.isNotEmpty()) {
                    item {
                        DetailSection(
                            title = "Episodes (${uiState.episodes.size})",
                            modifier = Modifier.padding(horizontal = 16.dp)
                        ) {
                            Column(
                                verticalArrangement = Arrangement.spacedBy(8.dp)
                            ) {
                                uiState.episodes.take(10).forEach { episode ->
                                    EpisodeItem(
                                        episode = episode,
                                        onClick = {
                                            onPlayEpisode(episode.malId.toString())
                                        }
                                    )
                                }
                                
                                if (uiState.episodes.size > 10) {
                                    TextButton(
                                        onClick = { /* Show all episodes */ }
                                    ) {
                                        Text("Show All Episodes")
                                    }
                                }
                            }
                        }
                    }
                }
                
                // Additional Info Section
                item {
                    DetailSection(
                        title = "Information",
                        modifier = Modifier.padding(horizontal = 16.dp)
                    ) {
                        Column(
                            verticalArrangement = Arrangement.spacedBy(8.dp)
                        ) {
                            anime.studios.firstOrNull()?.let { studio ->
                                InfoRow(label = "Studio", value = studio.name)
                            }
                            
                            anime.source?.let { source ->
                                InfoRow(label = "Source", value = source)
                            }
                            
                            anime.aired?.string?.let { aired ->
                                InfoRow(label = "Aired", value = aired)
                            }
                            
                            anime.rating?.let { rating ->
                                InfoRow(label = "Rating", value = rating)
                            }
                            
                            anime.popularity?.let { popularity ->
                                InfoRow(label = "Popularity", value = "#$popularity")
                            }
                            
                            anime.members?.let { members ->
                                InfoRow(label = "Members", value = members.toString())
                            }
                        }
                    }
                }
                
                item {
                    Spacer(modifier = Modifier.height(32.dp))
                }
            }
        }
    }
    
    // Error handling
    uiState.error?.let { error ->
        LaunchedEffect(error) {
            // Show error snackbar
            viewModel.clearError()
        }
    }
}

@Composable
fun DetailSection(
    title: String,
    modifier: Modifier = Modifier,
    content: @Composable () -> Unit
) {
    Column(modifier = modifier) {
        Text(
            text = title,
            style = MaterialTheme.typography.titleLarge,
            fontWeight = FontWeight.Bold,
            color = MaterialTheme.colorScheme.onSurface
        )
        
        Spacer(modifier = Modifier.height(12.dp))
        
        content()
        
        Spacer(modifier = Modifier.height(20.dp))
    }
}

@Composable
fun EpisodeItem(
    episode: Episode,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        onClick = onClick,
        modifier = modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surfaceVariant
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // Episode number
            Surface(
                shape = RoundedCornerShape(8.dp),
                color = MaterialTheme.colorScheme.primary,
                modifier = Modifier.size(40.dp)
            ) {
                Box(
                    contentAlignment = Alignment.Center,
                    modifier = Modifier.fillMaxSize()
                ) {
                    Text(
                        text = episode.episodeNumber.toString(),
                        style = MaterialTheme.typography.labelLarge,
                        color = Color.White,
                        fontWeight = FontWeight.Bold
                    )
                }
            }
            
            Spacer(modifier = Modifier.width(16.dp))
            
            // Episode info
            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = episode.title ?: "Episode ${episode.episodeNumber}",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Medium,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )
                
                episode.aired?.let { aired ->
                    Text(
                        text = aired,
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
            
            // Play icon
            Icon(
                imageVector = Icons.Default.PlayArrow,
                contentDescription = "Play",
                tint = MaterialTheme.colorScheme.primary
            )
        }
    }
}

@Composable
fun InfoRow(
    label: String,
    value: String,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        Text(
            text = label,
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant,
            modifier = Modifier.weight(1f)
        )
        
        Text(
            text = value,
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurface,
            modifier = Modifier.weight(2f)
        )
    }
}
