package com.animeplus.app.presentation.screens.player

import android.app.Activity
import android.content.pm.ActivityInfo
import androidx.activity.compose.BackHandler
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Fullscreen
import androidx.compose.material.icons.filled.FullscreenExit
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.media3.common.MediaItem
import androidx.media3.common.Player
import androidx.media3.exoplayer.ExoPlayer
import androidx.media3.ui.PlayerView
import com.animeplus.app.presentation.components.FullScreenError
import com.animeplus.app.presentation.components.FullScreenLoading
import com.animeplus.app.presentation.viewmodel.VideoPlayerViewModel

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun VideoPlayerScreen(
    episodeId: String,
    onBackClick: () -> Unit,
    viewModel: VideoPlayerViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsState()
    val context = LocalContext.current
    var isFullscreen by remember { mutableStateOf(false) }
    
    // Initialize player
    val exoPlayer = remember {
        ExoPlayer.Builder(context).build()
    }
    
    // Load episode data
    LaunchedEffect(episodeId) {
        viewModel.loadEpisode(episodeId)
    }
    
    // Handle streaming data
    LaunchedEffect(uiState.streamingData) {
        uiState.streamingData?.let { streamingData ->
            val mediaItem = MediaItem.fromUri(streamingData.sources.firstOrNull()?.url ?: "")
            exoPlayer.setMediaItem(mediaItem)
            exoPlayer.prepare()
            exoPlayer.playWhenReady = true
        }
    }
    
    // Handle fullscreen
    LaunchedEffect(isFullscreen) {
        val activity = context as? Activity
        activity?.requestedOrientation = if (isFullscreen) {
            ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE
        } else {
            ActivityInfo.SCREEN_ORIENTATION_PORTRAIT
        }
    }
    
    // Handle back press
    BackHandler {
        if (isFullscreen) {
            isFullscreen = false
        } else {
            exoPlayer.release()
            onBackClick()
        }
    }
    
    // Cleanup player
    DisposableEffect(Unit) {
        onDispose {
            exoPlayer.release()
        }
    }
    
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(Color.Black)
    ) {
        when {
            uiState.isLoading -> {
                FullScreenLoading("Loading episode...")
            }
            
            uiState.error != null -> {
                FullScreenError(
                    title = "Playback Error",
                    message = uiState.error,
                    onRetry = { viewModel.loadEpisode(episodeId) }
                )
            }
            
            uiState.streamingData != null -> {
                Column(
                    modifier = Modifier.fillMaxSize()
                ) {
                    // Top bar (only in portrait mode)
                    if (!isFullscreen) {
                        TopAppBar(
                            title = {
                                Column {
                                    Text(
                                        text = uiState.animeTitle ?: "Anime",
                                        style = MaterialTheme.typography.titleMedium,
                                        color = Color.White
                                    )
                                    Text(
                                        text = uiState.episodeTitle ?: "Episode $episodeId",
                                        style = MaterialTheme.typography.bodySmall,
                                        color = Color.White.copy(alpha = 0.7f)
                                    )
                                }
                            },
                            navigationIcon = {
                                IconButton(onClick = {
                                    exoPlayer.release()
                                    onBackClick()
                                }) {
                                    Icon(
                                        imageVector = Icons.Default.ArrowBack,
                                        contentDescription = "Back",
                                        tint = Color.White
                                    )
                                }
                            },
                            actions = {
                                IconButton(onClick = { isFullscreen = !isFullscreen }) {
                                    Icon(
                                        imageVector = if (isFullscreen) Icons.Default.FullscreenExit else Icons.Default.Fullscreen,
                                        contentDescription = if (isFullscreen) "Exit fullscreen" else "Enter fullscreen",
                                        tint = Color.White
                                    )
                                }
                            },
                            colors = TopAppBarDefaults.topAppBarColors(
                                containerColor = Color.Black
                            )
                        )
                    }
                    
                    // Video player
                    AndroidView(
                        factory = { context ->
                            PlayerView(context).apply {
                                player = exoPlayer
                                useController = true
                                setShowBuffering(PlayerView.SHOW_BUFFERING_WHEN_PLAYING)
                                
                                // Add fullscreen button listener
                                setFullscreenButtonClickListener { isFullScreen ->
                                    isFullscreen = isFullScreen
                                }
                            }
                        },
                        modifier = Modifier
                            .fillMaxWidth()
                            .weight(1f)
                    )
                    
                    // Episode info and controls (only in portrait mode)
                    if (!isFullscreen) {
                        EpisodeInfoSection(
                            animeTitle = uiState.animeTitle,
                            episodeTitle = uiState.episodeTitle,
                            episodeNumber = uiState.episodeNumber,
                            onNextEpisode = { viewModel.playNextEpisode() },
                            onPreviousEpisode = { viewModel.playPreviousEpisode() },
                            hasNextEpisode = uiState.hasNextEpisode,
                            hasPreviousEpisode = uiState.hasPreviousEpisode
                        )
                    }
                }
            }
        }
    }
}

@Composable
fun EpisodeInfoSection(
    animeTitle: String?,
    episodeTitle: String?,
    episodeNumber: Int?,
    onNextEpisode: () -> Unit,
    onPreviousEpisode: () -> Unit,
    hasNextEpisode: Boolean,
    hasPreviousEpisode: Boolean,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier
            .fillMaxWidth()
            .padding(16.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface
        )
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            // Episode info
            animeTitle?.let { title ->
                Text(
                    text = title,
                    style = MaterialTheme.typography.titleLarge,
                    color = MaterialTheme.colorScheme.onSurface
                )
            }
            
            episodeTitle?.let { title ->
                Text(
                    text = title,
                    style = MaterialTheme.typography.titleMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
            
            episodeNumber?.let { number ->
                Text(
                    text = "Episode $number",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // Navigation buttons
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Button(
                    onClick = onPreviousEpisode,
                    enabled = hasPreviousEpisode,
                    modifier = Modifier.weight(1f)
                ) {
                    Text("Previous Episode")
                }
                
                Spacer(modifier = Modifier.width(16.dp))
                
                Button(
                    onClick = onNextEpisode,
                    enabled = hasNextEpisode,
                    modifier = Modifier.weight(1f)
                ) {
                    Text("Next Episode")
                }
            }
        }
    }
}

// Placeholder ViewModel
@androidx.lifecycle.ViewModel
class VideoPlayerViewModel @javax.inject.Inject constructor() : androidx.lifecycle.ViewModel() {
    
    private val _uiState = kotlinx.coroutines.flow.MutableStateFlow(VideoPlayerUiState())
    val uiState: kotlinx.coroutines.flow.StateFlow<VideoPlayerUiState> = _uiState.asStateFlow()
    
    fun loadEpisode(episodeId: String) {
        // Implementation
    }
    
    fun playNextEpisode() {
        // Implementation
    }
    
    fun playPreviousEpisode() {
        // Implementation
    }
}

data class VideoPlayerUiState(
    val isLoading: Boolean = false,
    val streamingData: com.animeplus.app.data.model.StreamingData? = null,
    val animeTitle: String? = null,
    val episodeTitle: String? = null,
    val episodeNumber: Int? = null,
    val hasNextEpisode: Boolean = false,
    val hasPreviousEpisode: Boolean = false,
    val error: String? = null
)
