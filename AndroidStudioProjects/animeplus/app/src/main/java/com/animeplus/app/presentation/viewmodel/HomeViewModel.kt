package com.animeplus.app.presentation.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.animeplus.app.data.model.Anime
import com.animeplus.app.data.model.RecentEpisode
import com.animeplus.app.data.repository.AnimeRepository
import com.animeplus.app.data.repository.UserRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class HomeViewModel @Inject constructor(
    private val animeRepository: AnimeRepository,
    private val userRepository: UserRepository
) : ViewModel() {
    
    private val _uiState = MutableStateFlow(HomeUiState())
    val uiState: StateFlow<HomeUiState> = _uiState.asStateFlow()
    
    private val _isRefreshing = MutableStateFlow(false)
    val isRefreshing: StateFlow<Boolean> = _isRefreshing.asStateFlow()
    
    init {
        loadHomeData()
    }
    
    fun loadHomeData() {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true, error = null)
            
            try {
                // Load multiple data sources concurrently
                launch { loadTopAnime() }
                launch { loadCurrentSeasonAnime() }
                launch { loadUpcomingAnime() }
                launch { loadRecentEpisodes() }
                launch { loadRecentlyWatched() }
                launch { loadPopularStreamingAnime() }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = e.message ?: "Unknown error occurred"
                )
            }
        }
    }
    
    private suspend fun loadTopAnime() {
        animeRepository.getTopAnime(filter = "bypopularity", page = 1)
            .onSuccess { response ->
                _uiState.value = _uiState.value.copy(
                    topAnime = response.data,
                    isLoading = false
                )
            }
            .onFailure { error ->
                _uiState.value = _uiState.value.copy(
                    error = error.message,
                    isLoading = false
                )
            }
    }
    
    private suspend fun loadCurrentSeasonAnime() {
        animeRepository.getCurrentSeasonAnime(page = 1)
            .onSuccess { response ->
                _uiState.value = _uiState.value.copy(
                    currentSeasonAnime = response.data
                )
            }
            .onFailure { error ->
                // Handle error silently for secondary data
            }
    }
    
    private suspend fun loadUpcomingAnime() {
        animeRepository.getUpcomingSeasonAnime(page = 1)
            .onSuccess { response ->
                _uiState.value = _uiState.value.copy(
                    upcomingAnime = response.data
                )
            }
            .onFailure { error ->
                // Handle error silently for secondary data
            }
    }
    
    private suspend fun loadRecentEpisodes() {
        animeRepository.getRecentEpisodes(page = 1, type = 1)
            .onSuccess { response ->
                _uiState.value = _uiState.value.copy(
                    recentEpisodes = response.results
                )
            }
            .onFailure { error ->
                // Handle error silently for secondary data
            }
    }
    
    private suspend fun loadRecentlyWatched() {
        animeRepository.getRecentlyWatchedAnime(limit = 10)
            .collect { animeList ->
                _uiState.value = _uiState.value.copy(
                    recentlyWatched = animeList
                )
            }
    }
    
    private suspend fun loadPopularStreamingAnime() {
        animeRepository.getPopularStreamingAnime(page = 1)
            .onSuccess { response ->
                _uiState.value = _uiState.value.copy(
                    popularStreaming = response.results
                )
            }
            .onFailure { error ->
                // Handle error silently for secondary data
            }
    }
    
    fun refresh() {
        viewModelScope.launch {
            _isRefreshing.value = true
            try {
                // Force refresh from network
                loadTopAnime()
                loadCurrentSeasonAnime()
                loadUpcomingAnime()
                loadRecentEpisodes()
                loadPopularStreamingAnime()
            } finally {
                _isRefreshing.value = false
            }
        }
    }
    
    fun addToFavorites(anime: Anime) {
        viewModelScope.launch {
            userRepository.addToFavorites(anime)
                .onSuccess {
                    // Update UI state to reflect favorite status
                    val updatedTopAnime = _uiState.value.topAnime.map { 
                        if (it.malId == anime.malId) it.copy(isFavorite = true) else it 
                    }
                    _uiState.value = _uiState.value.copy(topAnime = updatedTopAnime)
                }
                .onFailure { error ->
                    _uiState.value = _uiState.value.copy(
                        error = "Failed to add to favorites: ${error.message}"
                    )
                }
        }
    }
    
    fun removeFromFavorites(animeId: Int) {
        viewModelScope.launch {
            userRepository.removeFromFavorites(animeId)
                .onSuccess {
                    // Update UI state to reflect favorite status
                    val updatedTopAnime = _uiState.value.topAnime.map { 
                        if (it.malId == animeId) it.copy(isFavorite = false) else it 
                    }
                    _uiState.value = _uiState.value.copy(topAnime = updatedTopAnime)
                }
                .onFailure { error ->
                    _uiState.value = _uiState.value.copy(
                        error = "Failed to remove from favorites: ${error.message}"
                    )
                }
        }
    }
    
    fun clearError() {
        _uiState.value = _uiState.value.copy(error = null)
    }
}

data class HomeUiState(
    val isLoading: Boolean = false,
    val topAnime: List<Anime> = emptyList(),
    val currentSeasonAnime: List<Anime> = emptyList(),
    val upcomingAnime: List<Anime> = emptyList(),
    val recentEpisodes: List<RecentEpisode> = emptyList(),
    val recentlyWatched: List<Anime> = emptyList(),
    val popularStreaming: List<com.animeplus.app.data.model.StreamingAnime> = emptyList(),
    val error: String? = null
)
