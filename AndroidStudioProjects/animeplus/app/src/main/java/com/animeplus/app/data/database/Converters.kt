package com.animeplus.app.data.database

import androidx.room.TypeConverter
import com.animeplus.app.data.model.*
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken

class Converters {
    
    private val gson = Gson()
    
    // AnimeImages converters
    @TypeConverter
    fun fromAnimeImages(images: AnimeImages?): String? {
        return images?.let { gson.toJson(it) }
    }
    
    @TypeConverter
    fun toAnimeImages(imagesString: String?): AnimeImages? {
        return imagesString?.let { 
            gson.fromJson(it, AnimeImages::class.java) 
        }
    }
    
    // AnimeAired converters
    @TypeConverter
    fun fromAnimeAired(aired: AnimeAired?): String? {
        return aired?.let { gson.toJson(it) }
    }
    
    @TypeConverter
    fun toAnimeAired(airedString: String?): AnimeAired? {
        return airedString?.let { 
            gson.fromJson(it, AnimeAired::class.java) 
        }
    }
    
    // List<AnimeGenre> converters
    @TypeConverter
    fun fromAnimeGenreList(genres: List<AnimeGenre>): String {
        return gson.toJson(genres)
    }
    
    @TypeConverter
    fun toAnimeGenreList(genresString: String): List<AnimeGenre> {
        val listType = object : TypeToken<List<AnimeGenre>>() {}.type
        return gson.fromJson(genresString, listType) ?: emptyList()
    }
    
    // List<AnimeStudio> converters
    @TypeConverter
    fun fromAnimeStudioList(studios: List<AnimeStudio>): String {
        return gson.toJson(studios)
    }
    
    @TypeConverter
    fun toAnimeStudioList(studiosString: String): List<AnimeStudio> {
        val listType = object : TypeToken<List<AnimeStudio>>() {}.type
        return gson.fromJson(studiosString, listType) ?: emptyList()
    }
    
    // List<AnimeProducer> converters
    @TypeConverter
    fun fromAnimeProducerList(producers: List<AnimeProducer>): String {
        return gson.toJson(producers)
    }
    
    @TypeConverter
    fun toAnimeProducerList(producersString: String): List<AnimeProducer> {
        val listType = object : TypeToken<List<AnimeProducer>>() {}.type
        return gson.fromJson(producersString, listType) ?: emptyList()
    }
    
    // List<String> converters (for genres in streaming models)
    @TypeConverter
    fun fromStringList(strings: List<String>): String {
        return gson.toJson(strings)
    }
    
    @TypeConverter
    fun toStringList(stringsString: String): List<String> {
        val listType = object : TypeToken<List<String>>() {}.type
        return gson.fromJson(stringsString, listType) ?: emptyList()
    }
    
    // Map<String, String> converters (for headers)
    @TypeConverter
    fun fromStringMap(map: Map<String, String>): String {
        return gson.toJson(map)
    }
    
    @TypeConverter
    fun toStringMap(mapString: String): Map<String, String> {
        val mapType = object : TypeToken<Map<String, String>>() {}.type
        return gson.fromJson(mapString, mapType) ?: emptyMap()
    }
    
    // List<StreamingSource> converters
    @TypeConverter
    fun fromStreamingSourceList(sources: List<StreamingSource>): String {
        return gson.toJson(sources)
    }
    
    @TypeConverter
    fun toStreamingSourceList(sourcesString: String): List<StreamingSource> {
        val listType = object : TypeToken<List<StreamingSource>>() {}.type
        return gson.fromJson(sourcesString, listType) ?: emptyList()
    }
    
    // List<StreamingEpisode> converters
    @TypeConverter
    fun fromStreamingEpisodeList(episodes: List<StreamingEpisode>): String {
        return gson.toJson(episodes)
    }
    
    @TypeConverter
    fun toStreamingEpisodeList(episodesString: String): List<StreamingEpisode> {
        val listType = object : TypeToken<List<StreamingEpisode>>() {}.type
        return gson.fromJson(episodesString, listType) ?: emptyList()
    }
    
    // UserPreferences converters
    @TypeConverter
    fun fromUserPreferences(preferences: UserPreferences): String {
        return gson.toJson(preferences)
    }
    
    @TypeConverter
    fun toUserPreferences(preferencesString: String): UserPreferences {
        return gson.fromJson(preferencesString, UserPreferences::class.java)
            ?: UserPreferences()
    }
}
