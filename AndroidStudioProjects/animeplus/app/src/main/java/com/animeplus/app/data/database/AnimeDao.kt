package com.animeplus.app.data.database

import androidx.room.*
import com.animeplus.app.data.model.*
import kotlinx.coroutines.flow.Flow

@Dao
interface AnimeDao {
    
    // Anime operations
    @Query("SELECT * FROM anime ORDER BY score DESC")
    fun getAllAnime(): Flow<List<Anime>>
    
    @Query("SELECT * FROM anime WHERE malId = :animeId")
    suspend fun getAnimeById(animeId: Int): Anime?
    
    @Query("SELECT * FROM anime WHERE title LIKE '%' || :query || '%' OR titleEnglish LIKE '%' || :query || '%'")
    fun searchAnime(query: String): Flow<List<Anime>>
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertAnime(anime: Anime)
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertAnimeList(animeList: List<Anime>)
    
    @Update
    suspend fun updateAnime(anime: Anime)
    
    @Delete
    suspend fun deleteAnime(anime: Anime)
    
    @Query("DELETE FROM anime")
    suspend fun clearAllAnime()
    
    // Favorites operations
    @Query("SELECT * FROM favorites ORDER BY addedAt DESC")
    fun getAllFavorites(): Flow<List<FavoriteAnime>>
    
    @Query("SELECT * FROM favorites WHERE animeId = :animeId")
    suspend fun getFavoriteById(animeId: Int): FavoriteAnime?
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertFavorite(favorite: FavoriteAnime)
    
    @Delete
    suspend fun deleteFavorite(favorite: FavoriteAnime)
    
    @Query("DELETE FROM favorites WHERE animeId = :animeId")
    suspend fun deleteFavoriteById(animeId: Int)
    
    @Query("SELECT EXISTS(SELECT 1 FROM favorites WHERE animeId = :animeId)")
    suspend fun isFavorite(animeId: Int): Boolean
    
    // Watch history operations
    @Query("SELECT * FROM watch_history ORDER BY lastWatchedAt DESC")
    fun getAllWatchHistory(): Flow<List<WatchHistory>>
    
    @Query("SELECT * FROM watch_history WHERE animeId = :animeId ORDER BY episodeNumber DESC")
    fun getWatchHistoryByAnime(animeId: Int): Flow<List<WatchHistory>>
    
    @Query("SELECT * FROM watch_history WHERE animeId = :animeId AND episodeId = :episodeId")
    suspend fun getWatchHistoryByEpisode(animeId: Int, episodeId: Int): WatchHistory?
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertWatchHistory(watchHistory: WatchHistory)
    
    @Update
    suspend fun updateWatchHistory(watchHistory: WatchHistory)
    
    @Delete
    suspend fun deleteWatchHistory(watchHistory: WatchHistory)
    
    @Query("DELETE FROM watch_history WHERE animeId = :animeId")
    suspend fun deleteWatchHistoryByAnime(animeId: Int)
    
    @Query("DELETE FROM watch_history")
    suspend fun clearAllWatchHistory()
    
    // Episodes operations
    @Query("SELECT * FROM episodes WHERE animeId = :animeId ORDER BY episodeNumber ASC")
    fun getEpisodesByAnime(animeId: Int): Flow<List<Episode>>
    
    @Query("SELECT * FROM episodes WHERE malId = :episodeId")
    suspend fun getEpisodeById(episodeId: Int): Episode?
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertEpisode(episode: Episode)
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertEpisodeList(episodes: List<Episode>)
    
    @Update
    suspend fun updateEpisode(episode: Episode)
    
    @Delete
    suspend fun deleteEpisode(episode: Episode)
    
    @Query("DELETE FROM episodes WHERE animeId = :animeId")
    suspend fun deleteEpisodesByAnime(animeId: Int)
    
    // User operations
    @Query("SELECT * FROM user WHERE uid = :uid")
    suspend fun getUserById(uid: String): User?
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertUser(user: User)
    
    @Update
    suspend fun updateUser(user: User)
    
    @Delete
    suspend fun deleteUser(user: User)
    
    @Query("DELETE FROM user")
    suspend fun clearAllUsers()
    
    // Statistics queries
    @Query("SELECT COUNT(*) FROM favorites")
    suspend fun getFavoritesCount(): Int
    
    @Query("SELECT COUNT(*) FROM watch_history")
    suspend fun getWatchHistoryCount(): Int
    
    @Query("SELECT COUNT(DISTINCT animeId) FROM watch_history")
    suspend fun getUniqueAnimeWatchedCount(): Int
    
    @Query("SELECT SUM(watchProgress) FROM watch_history")
    suspend fun getTotalWatchTime(): Long
    
    @Query("SELECT * FROM watch_history ORDER BY lastWatchedAt DESC LIMIT 10")
    fun getRecentWatchHistory(): Flow<List<WatchHistory>>
    
    @Query("SELECT * FROM favorites ORDER BY addedAt DESC LIMIT 10")
    fun getRecentFavorites(): Flow<List<FavoriteAnime>>
    
    // Search and filter queries
    @Query("""
        SELECT * FROM anime 
        WHERE (:query IS NULL OR title LIKE '%' || :query || '%' OR titleEnglish LIKE '%' || :query || '%')
        AND (:minScore IS NULL OR score >= :minScore)
        AND (:status IS NULL OR status = :status)
        AND (:year IS NULL OR year = :year)
        ORDER BY 
            CASE WHEN :sortBy = 'score' THEN score END DESC,
            CASE WHEN :sortBy = 'popularity' THEN popularity END ASC,
            CASE WHEN :sortBy = 'title' THEN title END ASC
    """)
    fun searchAnimeWithFilters(
        query: String?,
        minScore: Double?,
        status: String?,
        year: Int?,
        sortBy: String = "score"
    ): Flow<List<Anime>>
    
    // Cache management
    @Query("SELECT COUNT(*) FROM anime")
    suspend fun getAnimeCount(): Int
    
    @Query("DELETE FROM anime WHERE malId NOT IN (SELECT animeId FROM favorites)")
    suspend fun clearNonFavoriteAnime()
    
    @Query("SELECT * FROM anime WHERE lastWatched IS NOT NULL ORDER BY lastWatched DESC LIMIT :limit")
    fun getRecentlyWatchedAnime(limit: Int = 10): Flow<List<Anime>>
}
