# 🎌 Anime Plus - Android Application

Une application Android moderne et complète pour regarder des animes, développée en Kotlin avec Jetpack Compose.

![Anime Plus Banner](https://via.placeholder.com/800x200/E50914/FFFFFF?text=Anime+Plus+Android)

## ✨ Fonctionnalités

### 📱 Interface Utilisateur
- **Design moderne** inspiré de Netflix/Crunchyroll
- **Jetpack Compose** pour une UI native et fluide
- **Material Design 3** avec thème sombre/clair
- **Navigation intuitive** avec bottom navigation
- **Animations fluides** et transitions

### 🎬 Fonctionnalités Anime
- **Recherche avancée** avec filtres (genre, année, score)
- **Catalogue complet** avec animes populaires et tendances
- **Détails complets** : synopsis, genres, studios, épisodes
- **Lecteur vidéo intégré** avec ExoPlayer
- **Système de favoris** et watchlist
- **Historique de visionnage** avec reprise automatique

### 🔐 Authentification & Sync
- **Firebase Authentication** (email/password)
- **Synchronisation cloud** des favoris et historique
- **Profil utilisateur** avec statistiques
- **Notifications push** pour nouveaux épisodes

### 📊 APIs & Données
- **Jikan API** (MyAnimeList) pour métadonnées
- **GogoAnime API** pour streaming
- **Room Database** pour cache local
- **Mode hors-ligne** partiel

## 🛠️ Architecture Technique

### Stack Technologique
- **Kotlin** 100%
- **Jetpack Compose** pour l'UI
- **MVVM + Clean Architecture**
- **Hilt** pour l'injection de dépendances
- **Room** pour la base de données locale
- **Retrofit** pour les appels réseau
- **ExoPlayer** pour la lecture vidéo
- **Firebase** pour auth et notifications

### Structure du Projet
```
app/
├── src/main/java/com/animeplus/app/
│   ├── data/
│   │   ├── api/           # Services API
│   │   ├── database/      # Room Database
│   │   ├── model/         # Modèles de données
│   │   └── repository/    # Repositories
│   ├── di/                # Modules Hilt
│   ├── presentation/
│   │   ├── components/    # Composants réutilisables
│   │   ├── navigation/    # Navigation
│   │   ├── screens/       # Écrans de l'app
│   │   ├── theme/         # Thème et couleurs
│   │   └── viewmodel/     # ViewModels
│   ├── service/           # Services (Firebase, etc.)
│   ├── MainActivity.kt    # Activité principale
│   └── AnimePlusApplication.kt
└── src/main/res/
    ├── values/            # Strings, couleurs, dimensions
    └── xml/               # Configuration réseau
```

## 🚀 Installation et Configuration

### Prérequis
- **Android Studio** Hedgehog ou plus récent
- **JDK 17** ou plus récent
- **Android SDK** niveau 24+ (Android 7.0)
- **Compte Firebase** (optionnel pour auth)

### Installation
1. **Cloner le projet**
   ```bash
   git clone https://github.com/votre-username/anime-plus-android.git
   cd anime-plus-android
   ```

2. **Ouvrir dans Android Studio**
   - File → Open → Sélectionner le dossier du projet

3. **Configuration Firebase** (optionnel)
   - Créer un projet Firebase
   - Télécharger `google-services.json`
   - Placer dans `app/`

4. **Build et Run**
   - Sync Project with Gradle Files
   - Run 'app'

### Configuration API
Les APIs utilisées sont publiques et ne nécessitent pas de clés :
- **Jikan API** : `https://api.jikan.moe/v4/`
- **GogoAnime API** : `https://gogoanime.consumet.org/`

## 📱 Écrans de l'Application

### 🏠 Écran d'Accueil
- Carousel des animes populaires
- Sections : Tendances, Saison actuelle, À venir
- Continue watching pour utilisateurs connectés

### 🔍 Recherche
- Barre de recherche avec suggestions
- Filtres avancés (genre, année, score, statut)
- Résultats en grille avec pagination

### 📄 Détails Anime
- Hero section avec image et informations
- Synopsis, genres, studios
- Liste des épisodes
- Boutons Watch/Favoris

### 📺 Lecteur Vidéo
- ExoPlayer intégré
- Contrôles de lecture
- Mode plein écran
- Navigation épisodes

### ❤️ Favoris
- Liste des animes favoris
- Tri par date, titre, score
- Synchronisation cloud

### 👤 Profil
- Informations utilisateur
- Statistiques de visionnage
- Paramètres de l'app
- Authentification

## 🎨 Design System

### Couleurs
- **Primary** : Rouge Netflix (#E50914)
- **Secondary** : Bleu accent (#00D4FF)
- **Background** : Noir/Blanc selon le thème
- **Surface** : Gris foncé/Clair selon le thème

### Typographie
- **Poppins** pour les titres
- **Roboto** pour le corps de texte
- **Material Design 3** typography scale

### Composants
- Cards avec élévation
- Boutons Material 3
- Navigation bottom bar
- Chips pour genres
- Badges pour scores

## 🔧 Fonctionnalités Avancées

### Cache et Performance
- **Room Database** pour cache local
- **Coil** pour chargement d'images optimisé
- **Pagination** pour grandes listes
- **Lazy loading** des composants

### Sécurité
- **Network Security Config** pour HTTPS
- **ProGuard** pour obfuscation
- **Validation** des entrées utilisateur
- **Rate limiting** des APIs

### Accessibilité
- **Content descriptions** pour screen readers
- **Semantic properties** pour navigation
- **High contrast** support
- **Large text** support

## 🧪 Tests

### Tests Unitaires
```bash
./gradlew test
```

### Tests d'Interface
```bash
./gradlew connectedAndroidTest
```

### Tests de Performance
- **Baseline Profiles** pour optimisation
- **Memory leak detection**
- **Network monitoring**

## 📦 Build et Déploiement

### Debug Build
```bash
./gradlew assembleDebug
```

### Release Build
```bash
./gradlew assembleRelease
```

### Play Store
1. Générer un keystore
2. Configurer signing dans `build.gradle`
3. Build release APK/AAB
4. Upload sur Play Console

## 🤝 Contribution

1. **Fork** le projet
2. **Créer** une branche feature (`git checkout -b feature/AmazingFeature`)
3. **Commit** les changements (`git commit -m 'Add some AmazingFeature'`)
4. **Push** vers la branche (`git push origin feature/AmazingFeature`)
5. **Ouvrir** une Pull Request

### Guidelines
- Suivre les conventions Kotlin
- Utiliser Jetpack Compose
- Ajouter des tests pour nouvelles fonctionnalités
- Documenter les APIs publiques

## 📄 Licence

Ce projet est sous licence MIT. Voir le fichier [LICENSE](LICENSE) pour plus de détails.

## 🙏 Remerciements

- **Jikan API** pour les données MyAnimeList
- **Consumet API** pour les liens de streaming
- **Material Design** pour les guidelines UI
- **Jetpack Compose** pour le framework UI moderne

## 📞 Support

- **Issues** : [GitHub Issues](https://github.com/votre-username/anime-plus-android/issues)
- **Discussions** : [GitHub Discussions](https://github.com/votre-username/anime-plus-android/discussions)
- **Email** : <EMAIL>

---

**⭐ N'oubliez pas de mettre une étoile si ce projet vous a aidé !**
